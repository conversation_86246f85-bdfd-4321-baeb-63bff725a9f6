@isTest
private class ContentVersionTriggerHandlerTest {
    
    @testSetup
    static void setupTestData() {
        // Generate a unique username
        String uniqueUsername = 'testuser' + System.currentTimeMillis() + '@example.com.salesforce';

        // Create a test User with a unique username
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = uniqueUsername,
            CommunityNickname = 'testuser' + System.currentTimeMillis(),
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;

        // Create a test Account
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        // Create a test Opportunity
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity', 
            AccountId = testAccount.Id, 
            StageName = 'Prospecting', 
            CloseDate = System.today()
        );
        insert testOpportunity;

        // Create a test Project
        Project__c testProject = new Project__c(Loan_Opportunity__c = testOpportunity.Id);
        insert testProject;

        // Create a test Disbursement Request
        Disbursement_Request__c testDisbursement = new Disbursement_Request__c(Project_lookup__c = testProject.Id);
        insert testDisbursement;

        // Create test ContentVersion records pointing to different objects
        ContentVersion cv1 = new ContentVersion(Title = 'Test File 1', PathOnClient = 'TestFile1.pdf', VersionData = Blob.valueOf('Test Content 1'), FirstPublishLocationId = testProject.Id);
        ContentVersion cv2 = new ContentVersion(Title = 'Test File 2', PathOnClient = 'TestFile2.pdf', VersionData = Blob.valueOf('Test Content 2'), FirstPublishLocationId = testDisbursement.Id);
        ContentVersion cv3 = new ContentVersion(Title = 'Test File 3', PathOnClient = 'TestFile3.pdf', VersionData = Blob.valueOf('Test Content 3'), FirstPublishLocationId = testOpportunity.Id);

        insert new List<ContentVersion>{cv1, cv2, cv3};
    }

    @isTest
    static void testCreateActivity() {
        Test.startTest();
        
        // Retrieve inserted ContentVersions
        List<ContentVersion> versions = [SELECT Id, FirstPublishLocationId FROM ContentVersion];

        // Call the method
        ContentVersionTriggerHandler.createActivity(versions);

        Test.stopTest();

        // Verify that Activity_Logger__c records are created
        List<Activity_Logger__c> activities = [SELECT Id, Related_Record__c, Activity_Type__c FROM Activity_Logger__c];

        //System.assertEquals(3, activities.size(), 'There should be three activity log entries.');
        //for (Activity_Logger__c al : activities) {
            //System.assertEquals('File Uploaded', al.Activity_Type__c, 'Activity type should be File Uploaded.');
            //System.assertNotEquals(null, al.Related_Record__c, 'Related Record should be populated.');
        //}
    }
}
@SuppressWarnings('PMD')
public without sharing class StaticResourceController {
    
    @InvocableMethod 
    public static List<String> getStaticContentByName(List<String> names) {
        if (names == null || names.isEmpty()) return null;
        
        String name = names.get(0); // Assuming only one name is passed
        
        List<StaticResource> srObjList = new List<StaticResource>([SELECT Body FROM StaticResource WHERE Name = :name LIMIT 1]);

        Blob contentAsBlob = null;
        if(!srObjList.isEmpty()) {
            contentAsBlob = srObjList[0].Body;
        }
        
        List<String> result = new List<String>();
        if (contentAsBlob != null) {
            result.add(contentAsBlob.toString());
        }
        
        return result;
    }

    @AuraEnabled
    public static List<String> getStaticContentByNameAura(List<String> names) {
        if (names == null || names.isEmpty()) return null;
        
        String name = names.get(0); // Assuming only one name is passed
        
        List<StaticResource> srObjList = new List<StaticResource>([SELECT Body FROM StaticResource WHERE Name = :name LIMIT 1]);

        Blob contentAsBlob = null;
        if(!srObjList.isEmpty()) {
            contentAsBlob = srObjList[0].Body;
        }
        
        List<String> result = new List<String>();
        if (contentAsBlob != null) {
            result.add(contentAsBlob.toString());
        }
        
        return result;
    }
}
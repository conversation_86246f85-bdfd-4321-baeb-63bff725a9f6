@SuppressWarnings('PMD')
/**
 * @description Helper class for the LogEntryTrigger. Handles sending email notifications for ERROR level logs.
 */
public with sharing class LogEntryTriggerHelper {

    /**
     * @description Asynchronously sends email notifications for specified Log Entry records.
     * @param logEntryIds A Set of Ids for the Nebula__LogEntry__c records that triggered the notification.
     */
    @future // Use @future to run this asynchronously and avoid hitting limits in the trigger context
    public static void sendErrorNotifications(Set<Id> logEntryIds) {
        // --- 1. Get Email Recipients from Custom Label ---
        List<String> recipientEmails = new List<String>();
        String emailsFromLabel = '';
        try {
            // Retrieve the label value
            emailsFromLabel = System.Label.nebulaLoggerErrorReceipients;
            if (String.isNotBlank(emailsFromLabel)) {
                for (String email : emailsFromLabel.split(';')) {
                    email = email.trim();
                    if (String.isNotBlank(email)) {
                        recipientEmails.add(email);
                    }
                }
            }
        } catch (System.Exception e) { // Use general Exception to catch potential issues accessing the label
            System.debug(LoggingLevel.ERROR, 'Error accessing or processing Custom Label nebulaLoggerErrorReceipients. Error: ' + e.getMessage());
            // Optionally log this failure using Nebula Logger itself (be careful of loops)
            // Logger.error('Failed to retrieve/process email recipients from Custom Label nebulaLoggerErrorReceipients.', e);
            // Logger.saveLog();
            return; // Stop processing if recipients can't be determined
        }

        if (recipientEmails.isEmpty()) {
            System.debug(LoggingLevel.WARN, 'No recipient email addresses found in Custom Label nebulaLoggerErrorReceipients or the label is empty/invalid. Label Value: \'' + emailsFromLabel + '\'');
            return; // No one to send to
        }

        // --- 2. Query Log Entry Details ---
        List<Nebula__LogEntry__c> errorLogs = new List<Nebula__LogEntry__c>();
        try {
             errorLogs = [
                SELECT Id, Name, Nebula__Timestamp__c, Nebula__LoggingLevel__c, Nebula__Message__c,
                       Nebula__ExceptionMessage__c, Nebula__ExceptionStackTrace__c, Nebula__OriginLocation__c,
                       CreatedById, CreatedDate // Keep fields needed for the email
                FROM Nebula__LogEntry__c
                WHERE Id IN :logEntryIds
                ORDER BY Nebula__Timestamp__c DESC
            ];
        } catch (System.QueryException qe) {
             System.debug(LoggingLevel.ERROR, 'Failed to query LogEntry records. Error: ' + qe.getMessage());
             // Optionally log this failure
             return; // Stop processing if logs can't be queried
        }


        if (errorLogs.isEmpty()) {
            System.debug(LoggingLevel.INFO, 'No LogEntry records found for the provided Ids (query returned empty): ' + logEntryIds);
            return;
        }

        // --- 3. Construct HTML Email ---
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(recipientEmails);

        // Optional: Set Org-Wide Email Address if configured
        // List<OrgWideEmailAddress> owea = [SELECT Id FROM OrgWideEmailAddress WHERE Address = '<EMAIL>' LIMIT 1];
        // if (!owea.isEmpty()) {
        //     mail.setOrgWideEmailAddressId(owea[0].Id);
        // } else {
        //     mail.setSenderDisplayName('Salesforce Error Notification'); // Fallback display name
        // }
         mail.setSenderDisplayName('Salesforce Error Notification'); // Example display name

        // Construct Subject
        String subject = 'Salesforce ERROR Logged - ' + errorLogs.size() + ' Entry(s)';
        mail.setSubject(subject);

        // Get Base URL for links using the current standard method
        String baseUrl = URL.getOrgDomainUrl().toExternalForm();

        // Construct Body (HTML)
        String htmlBody = '<html><head><style>';
        // Basic CSS for better readability
        htmlBody += 'body { font-family: Arial, sans-serif; font-size: 10pt; }';
        htmlBody += 'table { border-collapse: collapse; margin-bottom: 15px; width: 95%; max-width: 800px; }'; // Added max-width
        htmlBody += 'th, td { border: 1px solid #dddddd; text-align: left; padding: 8px; vertical-align: top; }'; // Added vertical-align
        htmlBody += 'th { background-color: #f2f2f2; font-weight: bold; }';
        htmlBody += 'pre { white-space: pre-wrap; word-wrap: break-word; background-color: #f5f5f5; padding: 10px; border: 1px solid #eee; font-family: monospace; font-size: 9pt; }'; // Adjusted padding/font
        htmlBody += 'a { color: #0070d2; text-decoration: none; } a:hover { text-decoration: underline; }'; // Link styling
        htmlBody += '</style></head><body>';

        htmlBody += '<h2>Salesforce ERROR Notification</h2>';
        htmlBody += '<p>One or more ERROR level log entries were created in Salesforce.</p>';
        htmlBody += '<p><b>Number of Errors in this batch:</b> ' + errorLogs.size() + '</p>';
        htmlBody += '<hr style="border: 0; border-top: 1px solid #ccc;"/>'; // Styled hr

        Integer count = 1;
        for (Nebula__LogEntry__c log : errorLogs) {
            // Construct the full URL to the record
            String recordLink = baseUrl + '/' + log.Id;

            htmlBody += '<h3 style="color: #c23934;">Error #' + count + '</h3>'; // Added color to header
            htmlBody += '<table>';
            htmlBody += '<tr><th style="width:150px;">Field</th><th>Value</th></tr>'; // Fixed width for field column
            // Use String.valueOf for Id to prevent potential type issues if Name is null
            htmlBody += '<tr><td>Log Entry Record</td><td><a href="' + recordLink + '" target="_blank">' + escapeHtml(log.Name) + ' (' + String.valueOf(log.Id) + ')</a></td></tr>';
            htmlBody += '<tr><td>Timestamp</td><td>' + (log.Nebula__Timestamp__c != null ? String.valueOf(log.Nebula__Timestamp__c.formatGmt('yyyy-MM-dd HH:mm:ss z')) : 'N/A') + '</td></tr>'; // Use formatGmt for clarity
            htmlBody += '<tr><td>Origin</td><td>' + escapeHtml(log.Nebula__OriginLocation__c) + '</td></tr>';
            htmlBody += '<tr><td>Message</td><td>' + escapeHtml(log.Nebula__Message__c) + '</td></tr>';

            if (String.isNotBlank(log.Nebula__ExceptionMessage__c)) {
                 htmlBody += '<tr><td>Exception Message</td><td>' + escapeHtml(log.Nebula__ExceptionMessage__c) + '</td></tr>';
            }
            if (String.isNotBlank(log.Nebula__ExceptionStackTrace__c)) {
                 // Wrap stack trace in <pre> for better formatting
                 htmlBody += '<tr><td>Stack Trace</td><td><pre>' + escapeHtml(log.Nebula__ExceptionStackTrace__c) + '</pre></td></tr>';
            }
            htmlBody += '</table>';
            count++;
        }

        htmlBody += '<hr style="border: 0; border-top: 1px solid #ccc;"/>';
        htmlBody += '<p>Please review the linked log records in Salesforce for more details.</p>';
        htmlBody += '</body></html>';

        System.debug('htmlBody -> ' + htmlBody);
        mail.setHtmlBody(htmlBody);
        // Optional: Set plain text body as fallback
        // mail.setPlainTextBody('An ERROR level log entry was created... [See HTML version for details]');

        // --- 4. Send Email ---
        try {
            // Check governor limits before sending
            if (Limits.getEmailInvocations() < Limits.getLimitEmailInvocations()) {
                 Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
                 System.debug(LoggingLevel.INFO, 'Successfully sent HTML error notification email to: ' + String.join(recipientEmails, ', '));
            } else {
                 System.debug(LoggingLevel.WARN, 'Could not send error notification email. Governor limit for email invocations reached.');
                 // Optionally log this specific condition
            }
        } catch (System.EmailException e) {
            // Removed getStatusCode() as it doesn't exist on EmailException
            System.debug(LoggingLevel.ERROR, 'Failed to send HTML error notification email. Error: ' + e.getMessage());
            // Optionally log this failure
        }
    }

    /**
     * @description Helper method to escape HTML characters in strings to prevent rendering issues or XSS.
     * @param unsafe String potentially containing HTML characters.
     * @return String with HTML characters escaped. Returns an empty string if input is null or blank.
     */
    private static String escapeHtml(String unsafe) {
        if (String.isBlank(unsafe)) {
            return ''; // Return empty string for null or blank input
        }
        // Use Apex String method to escape HTML entities
        return unsafe.escapeHtml4();
    }
}
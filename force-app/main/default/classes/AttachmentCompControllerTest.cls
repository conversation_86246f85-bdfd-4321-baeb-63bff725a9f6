@isTest(SeeAllData=true)
private class AttachmentCompControllerTest {

    static testMethod void testDisbursementRequestChildRollup() {
        // ─── 1) Create parent & child records ──────────────────────────
        Disbursement_Request__c parentRec = new Disbursement_Request__c(
            //Name = 'DR Test'
            status__c = 'New'
        );
        insert parentRec;
        
        Requested_Item__c childRec = new Requested_Item__c(
            //Name                      = 'RI Test',
            Disbursement_Request__c   = parentRec.Id
        );
        insert childRec;
        
        // ─── 2) Verify the Custom Metadata exists ──────────────────────
        Custom_Files_Related_Rollups__mdt cfg = [
            SELECT MasterLabel, Roll_up__c, Parent__c 
              FROM Custom_Files_Related_Rollups__mdt 
             WHERE MasterLabel = 'Disbursement_Request__c'
             LIMIT 1
        ];
        System.assertEquals('Requested_Item__c', cfg.Roll_up__c);
        System.assertEquals('Disbursement_Request__c', cfg.Parent__c);
        
        // ─── 3) Create a file (ContentVersion) and link it to the child ─
        ContentVersion cv = new ContentVersion(
            Title        = 'ChildFile',
            PathOnClient = 'ChildFile.txt',
            VersionData  = Blob.valueOf('Hello, world!')
        );
        insert cv;
        // grab its DocumentId
        cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];
        
        Test.startTest();
            // uploadFiles on the child record
            AttachmentCompController.uploadFiles(
                childRec.Id, 
                new List<String>{ cv.ContentDocumentId }, 
                /* isInternal */ false
            );
        Test.stopTest();
        
        // ─── 4) Call getFilesOfRecord on the parent ────────────────────
        AttachmentCompController.FileFetchResult result =
            AttachmentCompController.getFilesOfRecord(parentRec.Id);
        
        // should have no parentFiles, but one childFiles entry
        System.assertEquals(0, result.parentFiles.size(), 'No files directly on parent');
        System.assertEquals(1, result.childFiles.size(),  'Exactly one file rolled up from child');
        System.assertEquals(
            cv.ContentDocumentId,
            result.childFiles[0].Id,
            'Child file Id should match'
        );
        
        // ─── 5) Call the SF‐wrapper version too ────────────────────────
        List<AttachmentCompController.ContentDocumentWrapper> wrappers =
            AttachmentCompController.getFilesOfRecordSF(
                parentRec.Id,
                /* showAllData */ true
            );
        System.assertEquals(1, wrappers.size(), 'SF‐wrapper should return one wrapper');
        System.assertEquals(
            childRec.Id,
            wrappers[0].contentDocumentLink.LinkedEntityId,
            'LinkedEntityId in wrapper must be the child record'
        );
        System.assertEquals(
            cv.ContentDocumentId,
            wrappers[0].contentDocument.Id,
            'Wrapped ContentDocument Id should match'
        );
    }
}
@SuppressWarnings('PMD')
/**
 * @description Provides utility methods to convert DynamoDB formatted JSON into
 *              standard Apex Map<String, Object> structures.
 *              Throws AuraHandledException for user-surfacing errors upon invalid input or format.
 */
public with sharing class DynamoHelper {

    // Set of recognized DynamoDB data type keys
    private static final Set<String> DYNAMO_TYPE_TOKENS = new Set<String>{
        'S', 'N', 'BOOL', 'M', 'L', 'B', 'SS', 'NS', 'BS'
        // Add 'NULL' here and handle in switch if needed for {"NULL": true} format
    };

    /**
     * @description Parses a DynamoDB formatted JSON string into a standard Apex Map<String, Object>.
     * @param dynamoJson The JSON string using DynamoDB's attribute value format (e.g., {"key": {"S": "value"}}).
     * @return Map<String, Object> A map representing the standard JSON structure. Returns an empty map if input is null or blank.
     * @throws AuraHandledException If the input JSON is malformed or contains unsupported DynamoDB types/formats during conversion.
     */
    @AuraEnabled // Add if calling from Lightning Component
    public static Map<String, Object> parse(String dynamoJson) {

        if (String.isBlank(dynamoJson)) {
            return new Map<String, Object>();
        }

        Object untypedJson;
        try {
            untypedJson = JSON.deserializeUntyped(dynamoJson);
        } catch (System.JSONException ex) {
            throw new AuraHandledException('The provided configuration data is not valid JSON. Please check the format. Details: ' + ex.getMessage());
        }

        try {
            Object result = unwrapDynamoValue(untypedJson);

            // The top level result should be a map for a DynamoDB item
            if (result instanceof Map<String, Object>) {
                return (Map<String, Object>) result;
            } else {
                // Corrected: Use helper to get type name instead of getSObjectType()
                String resultType = getApexTypeName(result);
                throw new AuraHandledException('The provided configuration data does not represent a standard DynamoDB item structure. Expected Map, got ' + resultType);
            }
        } catch (AuraHandledException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new AuraHandledException('An unexpected error occurred while processing the configuration data. Details: ' + ex.getMessage());
        }
    }

    /**
     * @description Recursively unwraps a DynamoDB formatted value.
     * @param dynamoValue The DynamoDB formatted value.
     * @return Object The unwrapped standard Apex value.
     * @throws AuraHandledException If conversion fails or unsupported types are found.
     */
    private static Object unwrapDynamoValue(Object dynamoValue) {
        if (dynamoValue == null) {
            return null;
        }

        if (dynamoValue instanceof List<Object>) {
             List<Object> dynamoList = (List<Object>) dynamoValue;
             List<Object> standardList = new List<Object>();
             for (Object element : dynamoList) {
                 standardList.add(unwrapDynamoValue(element)); // Recurse
             }
             return standardList;
         }

        if (!(dynamoValue instanceof Map<String, Object>)) {
            return dynamoValue;
        }

        Map<String, Object> dynamoMap = (Map<String, Object>) dynamoValue;

        if (dynamoMap.size() == 1) {
            String typeKey = dynamoMap.keySet().iterator().next();

            if (DYNAMO_TYPE_TOKENS.contains(typeKey)) {
                Object value = dynamoMap.get(typeKey);
                try {
                    switch on typeKey {
                        when 'S' { return (String) value; }
                        when 'N' {
                            if(value == null) {return null;}
                             // Ensure it's a string before trying to convert
                             if (!(value instanceof String) || String.isBlank((String)value)) {return null;}
                            return Decimal.valueOf((String)value);
                        }
                        when 'BOOL' { return (Boolean) value; }
                        when 'B' {
                             if(value == null) {return null;}
                             // Ensure it's a string before trying to decode
                             if (!(value instanceof String) || String.isBlank((String)value)) {return null;}
                            return EncodingUtil.base64Decode((String)value);
                        }
                        when 'SS' {
                            if (value instanceof List<Object>) { return convertToStringList((List<Object>)value, typeKey); }
                            else { handleInvalidFormat('SS', 'List', value); return null; }
                        }
                        when 'NS' {
                             if (value instanceof List<Object>) { return convertToDecimalList((List<Object>)value, typeKey); }
                             else { handleInvalidFormat('NS', 'List', value); return null; }
                        }
                        when 'BS' {
                             if (value instanceof List<Object>) { return convertToBlobList((List<Object>)value, typeKey); }
                             else { handleInvalidFormat('BS', 'List', value); return null; }
                        }
                        when 'L' {
                             if (value instanceof List<Object>) { return unwrapDynamoList((List<Object>)value); }
                             else { handleInvalidFormat('L', 'List', value); return null; }
                        }
                        when 'M' {
                            if (value instanceof Map<String, Object>) { return unwrapDynamoValue(value); } // Recurse
                            else { handleInvalidFormat('M', 'Map', value); return null; }
                        }
                        when else { handleUnsupportedType(typeKey); return null; }
                    }
                } catch (System.TypeException ex) {
                    throw new AuraHandledException('Error converting value for DynamoDB type ' + typeKey + '. Please check data format. Details: ' + ex.getMessage());
                 } catch (AuraHandledException ex) { throw ex; }
                   catch (Exception ex) { throw new AuraHandledException('An unexpected error occurred processing DynamoDB type ' + typeKey + '. Details: ' + ex.getMessage()); }
            }
        }

        Map<String, Object> standardMap = new Map<String, Object>();
        for (String key : dynamoMap.keySet()) {
            standardMap.put(key, unwrapDynamoValue(dynamoMap.get(key))); // Recurse
        }
        return standardMap;
    }

    // --- Helper methods for list conversions ---

    private static List<String> convertToStringList(List<Object> dynamoList, String typeKey) {
        List<String> stringList = new List<String>();
        if (dynamoList == null) {return stringList;}
        for(Object item : dynamoList) {
            if(item instanceof String) { stringList.add((String)item); }
            else if (item == null) { stringList.add(null); }
            else { handleInvalidListItem(typeKey, 'String', item); }
        }
        return stringList;
    }

    private static List<Decimal> convertToDecimalList(List<Object> dynamoList, String typeKey) {
        List<Decimal> decimalList = new List<Decimal>();
         if (dynamoList == null) {return decimalList;}
        for (Object item : dynamoList) {
             if (item instanceof String) {
                try { decimalList.add(Decimal.valueOf((String)item)); }
                catch(System.MathException ex) { throw new AuraHandledException('Invalid number format "' + item + '" found in Number Set (NS).'); }
            } else if (item == null) { decimalList.add(null); }
              else { handleInvalidListItem(typeKey, 'String (representing a number)', item); }
        }
        return decimalList;
    }

     private static List<Blob> convertToBlobList(List<Object> dynamoList, String typeKey) {
        List<Blob> blobList = new List<Blob>();
         if (dynamoList == null) {return blobList;}
        for (Object item : dynamoList) {
            if (item instanceof String) {
                 try { blobList.add(EncodingUtil.base64Decode((String)item)); }
                 catch(System.StringException ex) { throw new AuraHandledException('Invalid Base64 format found in Binary Set (BS). Item details suppressed.'); }
            } else if (item == null) { blobList.add(null); }
              else { handleInvalidListItem(typeKey, 'String (Base64 encoded)', item); }
        }
        return blobList;
    }

    private static List<Object> unwrapDynamoList(List<Object> dynamoList) {
         List<Object> standardList = new List<Object>();
          if (dynamoList == null) {return standardList;}
         for (Object item : dynamoList) { standardList.add(unwrapDynamoValue(item)); } // Recurse
         return standardList;
    }

    // --- Helper method to get Apex type name ---
    private static String getApexTypeName(Object obj) {
        if (obj == null) {return 'null';}
        if (obj instanceof String) {return 'String';}
        if (obj instanceof Decimal) {return 'Decimal';}
        if (obj instanceof Boolean) {return 'Boolean';}
        if (obj instanceof List<Object>) {return 'List';}
        if (obj instanceof Map<String, Object>) {return 'Map';}
        if (obj instanceof Blob) {return 'Blob';}
        if (obj instanceof Date) {return 'Date';}
        if (obj instanceof Datetime) {return 'Datetime';}
        if (obj instanceof Id) {return 'Id';}
        // Add other common types if needed
        return 'Object'; // Fallback for unknown types
    }


    // --- Helper methods for error handling (Using getApexTypeName) ---

    private static void handleInvalidListItem(String parentTypeKey, String expectedType, Object item) {
        // Corrected: Use helper to get type name instead of getSObjectType()
        String itemType = getApexTypeName(item);
        throw new AuraHandledException('Invalid item found in ' + parentTypeKey + ': Expected ' + expectedType + ', got ' + itemType);
    }

    private static void handleInvalidFormat(String parentTypeKey, String expectedFormat, Object value) {
        // Corrected: Use helper to get type name instead of getSObjectType()
         String actualFormat = getApexTypeName(value);
        throw new AuraHandledException('Invalid format for ' + parentTypeKey + ' value: Expected ' + expectedFormat + ', got ' + actualFormat);
    }

     private static void handleUnsupportedType(String typeKey) {
        throw new AuraHandledException('Unsupported DynamoDB type token encountered: ' + typeKey);
    }
}
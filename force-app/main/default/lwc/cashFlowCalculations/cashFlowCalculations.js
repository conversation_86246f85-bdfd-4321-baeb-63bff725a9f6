// cashFlowCalculations.js
/**
 * Helper module that exports two functions:
 *  1. calculateLoanDisbursementCashFlow(...)
 *  2. calculatePayApplicationCashFlow(...)
 *
 * Each function takes in the raw inputs (arrays of records) plus the existing
 * cashFlowData[] and returns a new, deep‐cloned cashFlowData array with the
 * appropriate rows ("MFLoanDisb", "PayAppSubmitted", etc.) updated.
 */

////////////////////////////////////////////////////////////////////////////////
// 1) Loan Disbursement → “MFLoanDisb” row update //////////////////////////////////
////////////////////////////////////////////////////////////////////////////////

export function calculateLoanDisbursementCashFlow(transactions, disbursements, cashFlowData) {
  // 1a) Derive projectId from disbursements array
  let projectId = null;
  if (Array.isArray(disbursements) && disbursements.length > 0) {
    projectId = disbursements[0].Project__c;
  }
  // If no projectId or no cashFlowData, just return as‐is
  if (!projectId || !Array.isArray(cashFlowData)) {
    return cashFlowData;
  }

  // 1b) Filter all transactions belonging to this project
  const txns = (Array.isArray(transactions) ? transactions : [])
    .filter((tx) => tx.Project__c === projectId);

  // 1c) Map each txn → { weekKey, weekDate, txId, loanDisb }
  const perTxn = txns.map((tx) => {
    const created = new Date(tx.CreatedDate);
    // Find the Friday of that week (or next Friday)
    const weekFriday = getNextFriday(created);

    return {
      weekKey: weekFriday.getTime(),
      weekDate: weekFriday,
      txId: tx.Id,
      loanDisb: tx.Loan_Principal_to_date__c || 0
    };
  });

  // 1d) Group by weekKey → { weekDate, loanDisbSum, items[] }
  const groups = perTxn.reduce((acc, curr) => {
    const key = curr.weekKey;
    if (!acc[key]) {
      acc[key] = {
        weekDate: curr.weekDate,
        loanDisbSum: 0,
        items: []
      };
    }
    acc[key].loanDisbSum += curr.loanDisb;
    acc[key].items.push({
      id: curr.txId,
      loanDisb: curr.loanDisb
    });
    return acc;
  }, {});

  // 2) Find the index of the “MFLoanDisb” row in the original cashFlowData
  const mfIndex = cashFlowData.findIndex((row) => row.id === "MFLoanDisb");
  if (mfIndex < 0) {
    // If no MFLoanDisb row, nothing to update
    return cashFlowData;
  }

  // 3) Deep‐clone cashFlowData, but only mutate the MFLoanDisb row
  const newCashFlowData = cashFlowData.map((row, idx) => {
    // If this is not the MFLoanDisb row, return it unchanged
    if (idx !== mfIndex) {
      return row;
    }

    // Otherwise, deep‐clone “MFLoanDisb” row and its weeks[]
    const clonedWeeks = Array.isArray(row.weeks)
      ? row.weeks.map((w) => ({ ...w }))
      : [];

    const clonedRow = {
      ...row,
      weeks: clonedWeeks
    };

    // 4) For each grouped Friday, find matching week.date === "YYYY-MM-DD"
    Object.values(groups).forEach((grp) => {
      const year = grp.weekDate.getFullYear();
      const month = String(grp.weekDate.getMonth() + 1).padStart(2, "0");
      const day = String(grp.weekDate.getDate()).padStart(2, "0");
      const isoDate = `${year}-${month}-${day}`;

      const match = clonedWeeks.find((wk) => wk.date === isoDate);
      if (match) {
        match.value = grp.loanDisbSum;
        match.dateLineItem = grp.items.slice(); // attach the line‐items
      }
    });

    return clonedRow;
  });

  return newCashFlowData;
}


////////////////////////////////////////////////////////////////////////////////
// 2) Pay Application → “ProjectedNetPayApp”, “PayAppSubmitted”, “LessRetainage”
////////////////////////////////////////////////////////////////////////////////

// export function calculatePayApplicationCashFlow(payApplications, transactions, projectData, cashFlowData) {
//   // 2a) Normalize inputs to arrays
//   const payApps = Array.isArray(payApplications)
//     ? payApplications
//     : payApplications
//     ? [payApplications]
//     : [];

//   const txns = Array.isArray(transactions)
//     ? transactions
//     : transactions
//     ? [transactions]
//     : [];

//   const projArray = Array.isArray(projectData)
//     ? projectData
//     : projectData
//     ? [projectData]
//     : [];

//   if (!Array.isArray(cashFlowData)) {
//     return cashFlowData;
//   }

//   // 2b) Sort transactions by CreatedDate (oldest → newest)
//   const sortedTxns = txns
//     .slice()
//     .sort((a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate));

//   // 2c) Build per‐transaction info: { weekKey, weekDate, grossPayApp, projectedPayment }
//   const perTxn = sortedTxns.map((tx) => {
//     const payAppId = tx.Related_Pay_Application__c;
//     let rawProjectedPayment = 0;
//     if (payAppId) {
//       const pa = payApps.find((p) => p.Id === payAppId);
//       if (pa && typeof pa.Projected_payment__c === "number") {
//         rawProjectedPayment = pa.Projected_payment__c;
//       }
//     }

//     const projectId = tx.Project__c;
//     const proj = projArray.find((p) => p.Id === projectId) || {};
//     const rawRetainage = typeof proj.Retainage__c === "number" ? proj.Retainage__c : 0;
//     const retainageFrac = rawRetainage / 100;

//     const grossPayApp = rawProjectedPayment * (1 - retainageFrac);
//     const weekKeyDate = getNextFriday(new Date(tx.CreatedDate));

//     return {
//       weekKey: weekKeyDate.getTime(),
//       weekDate: weekKeyDate,
//       payAppId,
//       grossPayApp,
//       projectedPayment: rawProjectedPayment
//     };
//   });

//   // 2d) Group by weekKey → { weekDate, grossSum, projectedSum, items[] }
//   const weeklyMap = perTxn.reduce((acc, obj) => {
//     const key = obj.weekKey;
//     if (!acc[key]) {
//       acc[key] = {
//         weekDate: obj.weekDate,
//         grossSum: 0,
//         projectedSum: 0,
//         items: []
//       };
//     }
//     acc[key].grossSum += obj.grossPayApp;
//     acc[key].projectedSum += obj.projectedPayment;
//     acc[key].items.push({
//       id: obj.payAppId,
//       grossPayApp: obj.grossPayApp,
//       projectedPayment: obj.projectedPayment
//     });
//     return acc;
//   }, {});

//   // 3) We need to update three distinct rows: "ProjectedNetPayApp", "PayAppSubmitted", "LessRetainage"
//   const newCashFlowData = cashFlowData.map((row) => {
//     if (
//       row.id !== "ProjectedNetPayApp" &&
//       row.id !== "PayAppSubmitted" &&
//       row.id !== "LessRetainage" &&
//       row.id !== "ReceiptPayApp"
//     ) {
//       return row;
//     }

//     // Deep‐clone the row + weeks[]
//     const clonedWeeks = Array.isArray(row.weeks)
//       ? row.weeks.map((w) => ({ ...w }))
//       : [];
//     const clonedRow = {
//       ...row,
//       weeks: clonedWeeks
//     };

//     // 4) For each week‐group, find matching week.date === "YYYY-MM-DD"
//     Object.values(weeklyMap).forEach((grp) => {
//       const year = grp.weekDate.getFullYear();
//       const month = String(grp.weekDate.getMonth() + 1).padStart(2, "0");
//       const day = String(grp.weekDate.getDate()).padStart(2, "0");
//       const isoDate = `${year}-${month}-${day}`;

//       const matchWeek = clonedWeeks.find((wk) => wk.date === isoDate);
//       if (!matchWeek) {
//         return;
//       }

//       // 4a) “ProjectedNetPayApp” row
//       if (row.id === "ProjectedNetPayApp") {
//         console.log('entering in ProjectedNetPayApp');
//         matchWeek.value = grp.grossSum;
//         matchWeek.projectedValue = grp.projectedSum;
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           grossPayApp: item.grossPayApp,
//           projectedPayment: item.projectedPayment
//         }));
//       }
//       // 4b) “PayAppSubmitted” row
//       else if (row.id === "PayAppSubmitted") {
//         console.log('entering in payappsubmitted');
//         matchWeek.value = grp.projectedSum;
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           projectedPayment: item.projectedPayment
//         }));
//       }
//       // 4c) “LessRetainage” row
//       else if (row.id === "LessRetainage") {
//         console.log('entering in LessRetainage');
//         const retainageTotal = grp.projectedSum - grp.grossSum;
//         matchWeek.value = -retainageTotal;
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           retainage: item.projectedPayment - item.grossPayApp
//         }));
//       }
//        else if (row.id === "ReceiptPayApp") {
//         console.log('entering in ReceiptPayApp');
//         matchWeek.value = grp.grossSum;
//         console.log("✅ Updating ReceiptPayApp for", isoDate, "=>", grp.grossSum);
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           receivedAmount: item.grossPayApp
//         }));
//       }
//     });

//     return clonedRow;
//   });

//   return newCashFlowData;
// }

export function calculatePayApplicationCashFlow(payApplications, transactions, projectData, cashFlowData) {
  const payApps = Array.isArray(payApplications) ? payApplications : [];
  const txns = Array.isArray(transactions) ? transactions : [];
  const projArray = Array.isArray(projectData) ? projectData : [];

  if (!Array.isArray(cashFlowData)) {
    return cashFlowData;
  }

  // Sort transactions by CreatedDate
  const sortedTxns = txns.slice().sort((a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate));

  // Create per-transaction payloads
  const perTxn = sortedTxns.map((tx) => {
    const payAppId = tx.Related_Pay_Application__c;
    let rawProjectedPayment = 0;

    if (payAppId) {
      const pa = payApps.find((p) => p.Id === payAppId);
      if (pa && typeof pa.Projected_payment__c === 'number') {
        rawProjectedPayment = pa.Projected_payment__c;
      }
    }

    const projectId = tx.Project__c;
    const proj = projArray.find((p) => p.Id === projectId) || {};
    const rawRetainage = typeof proj.Retainage__c === 'number' ? proj.Retainage__c : 0;
    const retainageFrac = rawRetainage / 100;

    const grossPayApp = rawProjectedPayment * (1 - retainageFrac);
    const weekKeyDate = getNextFriday(new Date(tx.CreatedDate));

    return {
      weekKey: weekKeyDate.getTime(),
      weekDate: weekKeyDate,
      payAppId,
      grossPayApp,
      projectedPayment: rawProjectedPayment
    };
  });

  // Group by weekKey
  const weeklyMap = perTxn.reduce((acc, obj) => {
    const key = obj.weekKey;
    if (!acc[key]) {
      acc[key] = {
        weekDate: obj.weekDate,
        grossSum: 0,
        projectedSum: 0,
        items: []
      };
    }
    acc[key].grossSum += obj.grossPayApp;
    acc[key].projectedSum += obj.projectedPayment;
    acc[key].items.push({
      id: obj.payAppId,
      grossPayApp: obj.grossPayApp,
      projectedPayment: obj.projectedPayment
    });
    return acc;
  }, {});

  // Update specific rows
  const newCashFlowData = cashFlowData.map((row) => {
    if (
      row.id !== 'ProjectedNetPayApp' &&
      row.id !== 'PayAppSubmitted' &&
      row.id !== 'LessRetainage' &&
      row.id !== 'ReceiptPayApp'
    ) {
      return row;
    }

    const clonedWeeks = Array.isArray(row.weeks) ? row.weeks.map((w) => ({ ...w })) : [];
    const clonedRow = { ...row, weeks: clonedWeeks };

    Object.values(weeklyMap).forEach((grp) => {
      const year = grp.weekDate.getFullYear();
      const month = String(grp.weekDate.getMonth() + 1).padStart(2, '0');
      const day = String(grp.weekDate.getDate()).padStart(2, '0');
      const isoDate = `${year}-${month}-${day}`;

      const matchWeek = clonedWeeks.find((wk) => wk.date === isoDate);
      if (!matchWeek) return;

      if (row.id === 'ProjectedNetPayApp') {
        matchWeek.value = grp.grossSum;
        matchWeek.projectedValue = grp.projectedSum;
        matchWeek.dateLineItem = grp.items.map((item) => ({
          id: item.id,
          grossPayApp: item.grossPayApp,
          projectedPayment: item.projectedPayment
        }));
      } else if (row.id === 'PayAppSubmitted') {
        matchWeek.value = grp.projectedSum;
        matchWeek.dateLineItem = grp.items.map((item) => ({
          id: item.id,
          projectedPayment: item.projectedPayment
        }));
      } else if (row.id === 'LessRetainage') {
        const retainageTotal = grp.projectedSum - grp.grossSum;
        matchWeek.value = -retainageTotal;
        matchWeek.dateLineItem = grp.items.map((item) => ({
          id: item.id,
          retainage: item.projectedPayment - item.grossPayApp
        }));
      } else if (row.id === 'ReceiptPayApp') {
        // 🟢 Add logic to populate "Receipt of Pay App"
        matchWeek.value = grp.grossSum;
        matchWeek.dateLineItem = grp.items.map((item) => ({
          id: item.id,
          receivedAmount: item.grossPayApp
        }));
      }
    });

    return clonedRow;
  });

  return newCashFlowData;
}



////////////////////////////////////////////////////////////////////////////////
//  Utility: Given a JS Date, return the Date object for that week’s Friday. //
////////////////////////////////////////////////////////////////////////////////
function getNextFriday(date) {
  const d = new Date(date);
  const dow = d.getDay(); // Sunday=0 … Friday=5 … Saturday=6
  const offset = (5 - dow + 7) % 7;
  d.setDate(d.getDate() + offset);
  d.setHours(0, 0, 0, 0);
  return d;
}
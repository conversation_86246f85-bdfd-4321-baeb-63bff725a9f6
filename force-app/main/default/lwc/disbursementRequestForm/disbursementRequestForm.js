import { LightningElement, track, wire, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference, NavigationMixin } from 'lightning/navigation';

// Apex methods
import getProjectObj from '@salesforce/apex/TakeParamFromUrlController.getProjectObj';
import getStylesStaticResource from '@salesforce/apex/StaticResourceController.getStaticContentByNameAura';
// import getDisbursementPicklistValues from '@salesforce/apex/DisbursementFormController.getDisbursementPicklistValues'; // Uncommented in original, assuming it's needed
import submitDisbursementRequest from '@salesforce/apex/DisbursementFormController.submitDisbursementRequest';
import getPausedFormState from '@salesforce/apex/DisbursementFormController.getPausedFormState';
import savePausedFormState from '@salesforce/apex/DisbursementFormController.savePausedFormState';
import completePausedFormState from '@salesforce/apex/DisbursementFormController.completePausedFormState';

export default class DisbursementRequestForm extends NavigationMixin(LightningElement) {
    @api recordId; // Project ID from page context or URL

    pageRef;

    @track stylesCss;
    @track isLoadingInitialData = true;
    @track isSubmitting = false;
    @track isSavingForLater = false;
    @track errorMessageText = '';
    @track showSuccessMessage = false;
    @track disbursementRecordId;

    showBankInfo = false;
    showMailCheckTo = false;
    showAddress = false;

    @track formState = {
        urlProjectId: null,
        urlPausedFormId: null,
        initialData: {
            loanId: '',
            loanName: '',
            projectId: '',
            projectName: '',
            accountName: '',
            contactName: '',
            contactId: '',
            contactTitle: '',
            effectiveDateofLoan: '',
            gcOwnerNameForPerjury: '',
            projectNameForPerjury: '',
            projectOwner: '',
            amountRequestedFromLoan: ''
        },
        formData: {
            expenseType: '',
            selectedPayeeId: null,
            payeeName: '',
            payeeFirstName: '',
            payeeLastName: '',
            payeeContactEmail: '',
            paymentMethod: '',
            payeeStreet: '',
            payeeCity: '',
            payeeState: '',
            payeeZip: '',
            payeeCountry: '',
            mailCheckTo: '',
            bankName: '',
            bankAccountName: '',
            bankRoutingNumber: '',
            bankAccountNumber: '',
            payeePhone: '',
            yourName: '',
            requesterEmail: '',
            additionalComments: '',
            clientSignature: null, // Initialize as null
            iAgreePerjury: false,
        },
        requestItemsJSON: '[]',
        mainUploadedFileDetails: [],
        totalInvoiceAmount: 0,
    };

    @track expenseTypeOptions = [
        { label: '--- None ---', value: '' },
        { label: 'Direct Employee Labor', value: 'Direct Employee Labor' },
        { label: 'Subcontract Labor', value: 'Subcontract Labor' },
        { label: 'Material', value: 'Material' },
        { label: 'Equipment Rental', value: 'Equipment Rental' },
        { label: 'Bond Premium', value: 'Bond Premium' },
        { label: 'Misc.', value: 'Misc.' },
        { label: 'Final Disbursement Payoff', value: 'Final Disbursement Payoff' }
    ];
    @track paymentMethodOptions = [
        { label: '--- None ---', value: '' },
        { label: 'Check', value: 'Check' },
        { label: 'Wire', value: 'Wire' },
        { label: 'ACH', value: 'ACH' }
    ];
    @track totalInvoiceAmountString = '0.00';

    @wire(CurrentPageReference)
    wiredPageRef(pageRef) {
        if (pageRef) {
            this.pageRef = pageRef;
            const projectIdFromUrl = this.recordId || pageRef.state.recordId || pageRef.state.c__recordId; // Common community URL param
            const pausedIdFromUrl = pageRef.state.pausedId || pageRef.state.c__pausedId;

            if (pausedIdFromUrl) {
                this.formState.urlPausedFormId = pausedIdFromUrl;
                this.loadPausedState(pausedIdFromUrl);
            } else if (projectIdFromUrl) {
                this.formState.urlProjectId = projectIdFromUrl;
                this.formState.initialData.projectId = projectIdFromUrl;
                this.loadInitialProjectData(projectIdFromUrl);
            } else {
                this.isLoadingInitialData = false;
                this.showToast('Info', 'No Project ID or Paused Form ID provided.', 'info');
                console.warn('Disbursement Form: No Project ID or Paused Form ID found in URL.');
            }
        }
    }

    connectedCallback() {
        this.loadStyles();
        // this.loadPicklistValues(); // Consider calling this if not pre-defined or if it's dynamic
        console.log('DisbursementRequestForm connected.');
    }

    async loadInitialProjectData(projectId) {
        if (this.formState.urlPausedFormId && !this.formState.initialData.projectName) { // Don't re-fetch if loading paused state that already has data
             console.log('loadInitialProjectData skipped as paused form is loading/loaded project data');
        } else if (!projectId){
            console.error('loadInitialProjectData called with no projectId');
            this.isLoadingInitialData = false;
            return;
        }


        this.isLoadingInitialData = true;
        console.log('loadInitialProjectData for projectId: ' + projectId);

        try {
            const result = await getProjectObj({ projectId: projectId });
            console.log('getProjectObj result: ' + JSON.stringify(result));
            
            if (result) {
                let projectData = result.project;
                let contactData = result.contact;

                this.formState.initialData = {
                    ...this.formState.initialData,
                    loanId: projectData?.Loan_Opportunity__c,
                    loanName: projectData?.Loan_Opportunity__r?.Name,
                    projectId: this.formState.initialData.projectId || projectData?.Id, // Ensure projectId is preserved
                    projectName: projectData?.Name,
                    accountName: projectData?.Account_Name__r?.Name,
                    contactName: contactData?.Name,
                    contactId: contactData?.Id,
                    contactTitle: contactData?.Title,
                    effectiveDateofLoan: projectData?.Loan_Opportunity__r?.Effective_Date_of_Loan_Docs__c,
                    gcOwnerNameForPerjury: projectData?.Disbursement_Requests1_del__r?.length > 0
                        ? projectData.Disbursement_Requests1_del__r[0].General_Contractor_Contract_Owner__c
                        : projectData?.Account_Name__r?.Name, // Fallback for perjury name
                    projectNameForPerjury: projectData?.Name,
                    projectOwner: projectData?.Enter_name_of_Project_Owner__c,
                    amountRequestedFromLoan: projectData?.Loan_Opportunity__r?.Loan_Amount_Requested__c
                };
                // Set 'Your Name' only if not already set by paused state later
                if (!this.formState.formData.yourName) {
                     this.formState.formData.yourName = this.formState.initialData.contactName || this.formState.initialData.accountName;
                }
                console.log('formState.initialData populated: ' + JSON.stringify(this.formState.initialData));
            } else {
                 this.showToast('Warning', 'Project data not found for the provided ID.', 'warning');
            }
        } catch (error) {
            console.error('Error loading initial project data:', error);
            this.errorMessageText = this.getErrorMessage(error);
            this.showToast('Error', `Failed to load project data: ${this.errorMessageText}`, 'error');
        } finally {
            this.isLoadingInitialData = false;
        }
    }

    async loadPausedState(pausedFormId) {
        this.isLoadingInitialData = true;
        console.log('Loading paused state for pausedFormId:', pausedFormId);
        try {
            const stateJSON = await getPausedFormState({ pausedFormId: pausedFormId });
            if (stateJSON) {
                const loadedState = JSON.parse(stateJSON);
                console.log('Paused state loaded:', loadedState);

                // Merge initialData, ensuring projectId from URL isn't overwritten if loadedState doesn't have it
                const currentProjectId = this.formState.initialData.projectId;
                this.formState.initialData = { ...this.formState.initialData, ...loadedState.initialData };
                if (currentProjectId && !this.formState.initialData.projectId) {
                    this.formState.initialData.projectId = currentProjectId;
                }
                
                this.formState.formData = { ...this.formState.formData, ...loadedState.formData };
                this.formState.requestItemsJSON = loadedState.requestItemsJSON || '[]';
                this.formState.mainUploadedFileDetails = loadedState.mainUploadedFileDetails || [];
                this.formState.totalInvoiceAmount = loadedState.totalInvoiceAmount || 0;
                
                this.totalInvoiceAmountString = parseFloat(this.formState.totalInvoiceAmount).toFixed(2);

                // If project data wasn't fully loaded in initialData (e.g. project name), and we have a project ID, fetch it.
                if (this.formState.initialData.projectId && !this.formState.initialData.projectName) {
                    console.log('Paused state loaded, but project details like name are missing. Fetching project details.');
                    await this.loadInitialProjectData(this.formState.initialData.projectId);
                }
                
                // Ensure 'Your Name' is set
                if (!this.formState.formData.yourName) {
                    this.formState.formData.yourName = this.formState.initialData.contactName || this.formState.initialData.accountName || '';
                }
                // Crucially, update the signature component if a signature was saved
                if (this.formState.formData.clientSignature) {
                    const signatureComp = this.template.querySelector('c-signature-data-cmp');
                    if (signatureComp) {
                        signatureComp.clientSignature = this.formState.formData.clientSignature;
                    }
                }

                this.showToast('Info', 'Paused form loaded.', 'info');
            } else {
                this.showToast('Error', 'Could not load paused form data. Starting new form.', 'error');
                 if (this.formState.urlProjectId) {
                    this.loadInitialProjectData(this.formState.urlProjectId);
                }
            }
        } catch (error) {
            console.error('Error loading paused state:', error);
            this.errorMessageText = this.getErrorMessage(error);
            this.showToast('Error', `Failed to load paused state: ${this.errorMessageText}`, 'error');
             if (this.formState.urlProjectId) { // Fallback if paused load fails but we had a project id
                this.loadInitialProjectData(this.formState.urlProjectId);
            }
        } finally {
            this.isLoadingInitialData = false;
        }
    }

    async loadStyles() {
        try {
            this.stylesCss = await getStylesStaticResource({ resourceName: 'Styles' });
        } catch (error) {
            console.error('Error loading styles:', error);
        }
    }

    handlePayeeSelectionChange(event) {
        const payeeData = event.detail;
        console.log('payeeData -> ', JSON.stringify(payeeData));
        this.formState.formData.selectedPayeeId = payeeData.selectedIdOutput || null;
        this.formState.formData.payeeName = payeeData.payeeName || '';
        this.formState.formData.paymentMethod = payeeData.paymentMethod || this.formState.formData.paymentMethod;
        this.formState.formData.payeeFirstName = payeeData.payeeFirstName || '';
        this.formState.formData.payeeLastName = payeeData.payeeLastName || '';
        this.formState.formData.payeeContactEmail = payeeData.payeeContactEmail || '';
        this.formState.formData.payeePhone = payeeData.payeePhone || '';
        this.formState.formData.mailCheckTo = payeeData.Mail_Check_To || '';
        this.formState.formData.bankName = payeeData.Bank_Name || '';
        this.formState.formData.bankAccountName = payeeData.Account_Name || '';
        this.formState.formData.bankRoutingNumber = payeeData.Bank_Routing_Number || '';
        this.formState.formData.bankAccountNumber = payeeData.Bank_Account_Number || '';
        this.formState.formData.payeeStreet = payeeData.Street || '';
        this.formState.formData.payeeCity = payeeData.City || '';
        this.formState.formData.payeeState = payeeData.State || '';
        this.formState.formData.payeeZip = payeeData.Zip || '';
        this.formState.formData.payeeCountry = payeeData.Country || '';

        // Update conditional visibility for payment method fields if payment method changed
        if(payeeData.paymentMethod) {
            this.updatePaymentMethodVisibility(payeeData.paymentMethod);
        }
        console.log('formState -> ', JSON.stringify(this.formState));
        this.formState = { ...this.formState };
    }
    
    handleRepeaterChange(event) {
        console.log('event.detail -> ', JSON.stringify(event.detail)); 
        if (event.detail) {
            this.formState.requestItemsJSON = event.detail.requestItemsNew || '[]';
            const newTotal = parseFloat(event.detail.totalInvoiceAmount);
            this.formState.totalInvoiceAmount = isNaN(newTotal) ? 0 : newTotal;
            this.totalInvoiceAmountString = this.formState.totalInvoiceAmount.toFixed(2);
        } else { // Fallback to querying component if event detail is not as expected
            const repeater = this.template.querySelector('c-request-item-repeater');
            if (repeater) {
                this.formState.requestItemsJSON = repeater.requestItemsJSON || '[]';
                const newTotal = parseFloat(repeater.totalInvoiceAmount);
                this.formState.totalInvoiceAmount = isNaN(newTotal) ? 0 : newTotal;
                this.totalInvoiceAmountString = this.formState.totalInvoiceAmount.toFixed(2);
            }
        }
    }

    handleMainUploadFinished(event) {
        const uploadedFiles = event.detail.files;
        this.formState.mainUploadedFileDetails = [
            ...(this.formState.mainUploadedFileDetails || []),
            ...uploadedFiles.map(file => ({ documentId: file.documentId, name: file.name }))
        ];
        this.showToast('Success', `${uploadedFiles.length} file(s) uploaded.`, 'success');
    }

    handlePerjuryChange(event) {
        console.log('event.detail -> ', event.detail);
        console.log('event.detail -> ', JSON.stringify(event.detail));
        if (event.detail) {
            this.formState.formData.iAgreePerjury = event.detail.value;
        }
          
        
    }

    handleSignatureChange(event) {
        console.log('DisbursementForm: handleSignatureChange triggered.');
        if (event.detail && typeof event.detail.signature !== 'undefined') { // Check if signature property exists
            this.formState.formData.clientSignature = event.detail.signature;
            console.log('Signature updated in formState:', !!event.detail.signature);
        } else {
             this.formState.formData.clientSignature = null; // Explicitly nullify if event format is unexpected
             console.warn('Signature event did not contain expected detail.signature property');
        }
    }

    handleInputChange(event) {
        const { name, value, type, checked, label } = event.target;
        const fieldName = name || label; // Fallback to label if name is not on target
        console.log('handleInputChange:', fieldName, value, type, checked);
        if (type === 'checkbox') {
            this.formState.formData[fieldName] = checked;
        } else {
            this.formState.formData[fieldName] = value;
        }
        // this.formState = { ...this.formState }; // No need to spread entire formState for formData change
    }
    
    updatePaymentMethodVisibility(paymentMethodValue) {
        this.showBankInfo = (paymentMethodValue === 'ACH' || paymentMethodValue === 'Wire');
        this.showMailCheckTo = (paymentMethodValue === 'Check');
        this.showAddress = (paymentMethodValue === 'Check');
    }

    handlePaymentMethodChange(event) {
        const newPaymentMethod = event.target.value;
        this.formState.formData.paymentMethod = newPaymentMethod;
        console.log('Payment method changed to -> '+ newPaymentMethod);
        this.updatePaymentMethodVisibility(newPaymentMethod);
    }

    handleAddressChange(event) {
        this.formState.formData.payeeStreet = event.target.street;
        this.formState.formData.payeeCity = event.target.city;
        this.formState.formData.payeeState = event.target.province;
        this.formState.formData.payeeZip = event.target.postalCode;
        this.formState.formData.payeeCountry = event.target.country;
    }

    get todayDate() { return new Date().toISOString().slice(0,10); }
    get acceptedFileFormats() { return '.doc,.docx,.xls,.xlsx,.pdf,.png,.csv,.jpg'; }
    get additionalCommentsLength() { return this.formState.formData.additionalComments ? this.formState.formData.additionalComments.length : 0; }
    get spinnerActionText() { return this.isSubmitting ? 'Submitting...' : (this.isSavingForLater ? 'Saving...' : 'Loading...'); }


    validateForm() {
        let isValid = true;
        // Validate standard inputs
        this.template.querySelectorAll('lightning-input, lightning-combobox, lightning-textarea, lightning-input-address').forEach(input => {
            if (input.reportValidity && !input.reportValidity()) {
                isValid = false;
            }
        });

        // Validate c-request-item-repeater
        const repeater = this.template.querySelector('c-request-item-repeater');
        if (repeater && typeof repeater.validate === 'function') {
            const repeaterValidation = repeater.validate();
            if(repeaterValidation && !repeaterValidation.isValid) {
                isValid = false;
                this.showToast('Error', repeaterValidation.errorMessage || 'Please complete all required fields in the Item Description section.', 'error');
            }
        }
        console.log('this.formState -> ', JSON.stringify(this.formState));

        // Validate c-perjury-statement-language (assuming direct DOM query or it dispatches an event handled by handlePerjuryChange)
        if (!this.formState.formData.iAgreePerjury) {
             const perjuryComp = this.template.querySelector('c-perjury-statement-language');
             // Try to make perjury component report validity if it has such a method
             if (perjuryComp && typeof perjuryComp.reportValidity === 'function' && !perjuryComp.reportValidity()) {
                isValid = false;
             } else if (!this.formState.formData.iAgreePerjury) { // Fallback if no reportValidity
                this.showToast('Error', 'You must agree to the terms in the Perjury Statement.', 'error');
                isValid = false;
             }
        }

        // Validate c-signature-data-cmp
        const signaturePad = this.template.querySelector('c-signature-data-cmp');
        if (signaturePad && typeof signaturePad.validate === 'function') {
            const signatureValidation = signaturePad.validate();
            if (signatureValidation && !signatureValidation.isValid) {
                this.showToast('Error', signatureValidation.errorMessage || 'Signature is required.', 'error');
                isValid = false;
            }
        } else if (!this.formState.formData.clientSignature) { // Fallback if no validate method or direct check
             this.showToast('Error', 'Signature is required.', 'error');
             isValid = false;
        }
        return isValid;
    }
    
    async handleSaveForLater() {
        this.errorMessageText = '';
        this.isSavingForLater = true;

        // Ensure latest repeater data is captured
        const repeater = this.template.querySelector('c-request-item-repeater');
        if (repeater) {
            this.formState.requestItemsJSON = repeater.requestItemsJSON || '[]'; // Assuming property access
            this.formState.totalInvoiceAmount = parseFloat(repeater.totalInvoiceAmount) || 0; // Assuming property access
        }
        
        const stateToSave = { ...this.formState };
        // Remove parts not meant for saving or too large if necessary (e.g., file content if stored, but here it's IDs)
        const stateToSaveJSON = JSON.stringify(stateToSave);

        try {
            const savedPausedId = await savePausedFormState({
                jsonState: stateToSaveJSON,
                existingPausedId: this.formState.urlPausedFormId,
                projectId: this.formState.initialData.projectId
            });
            this.formState.urlPausedFormId = savedPausedId; // Update the current paused ID
            this.showToast('Success', 'Your progress has been saved.', 'success');
        } catch (error) {
            console.error('Error saving for later:', error);
            this.errorMessageText = this.getErrorMessage(error);
            this.showToast('Error Saving Progress', this.errorMessageText, 'error');
        } finally {
            this.isSavingForLater = false;
        }
    }

    async handleSubmit() {
        console.log('handleSubmit started');
        this.errorMessageText = '';
        if (!this.validateForm()) {
            console.log('Form validation failed');
            this.showToast('Error', 'Please review the form and correct any errors.', 'error');
            return;
        }
        console.log('Form validation successful');
        console.log('this.formState.initialData -> ', JSON.stringify(this.formState.initialData));
        console.log('this.formState.formData -> ', JSON.stringify(this.formState.formData));
        console.log('this.formState.requestItemsJSON -> ', JSON.stringify(this.formState.requestItemsJSON));
        console.log('this.formState.mainUploadedFileDetails -> ', JSON.stringify(this.formState.mainUploadedFileDetails));
        console.log('this.formState.totalInvoiceAmount -> ', this.formState.totalInvoiceAmount);

        this.isSubmitting = true;

        let requestData = {
            initialDetails: { ...this.formState.initialData },
            formDetails: { ...this.formState.formData },
            requestItemsJSON: (this.formState.requestItemsJSON || '[]'),
            mainUploadedDocumentIds: this.formState.mainUploadedFileDetails.map(f => f.documentId),
            totalAmount: this.formState.totalInvoiceAmount
        };
        console.log('Request data prepared:', JSON.stringify(requestData));

        try {
            console.log('Submitting disbursement request...');
            const resultDisbursementId = await submitDisbursementRequest({ requestWrapperJSON: JSON.stringify(requestData) });
            this.disbursementRecordId = resultDisbursementId;
            console.log('Disbursement request submitted successfully. Disbursement ID:', resultDisbursementId);
            
            if (this.formState.urlPausedFormId) {
                console.log('Completing paused form state for ID:', this.formState.urlPausedFormId);
                await completePausedFormState({ pausedFormId: this.formState.urlPausedFormId });
                console.log('Paused form state completed.');
            }
            this.showSuccessMessage = true; // This will trigger navigation via the template
            // Toast is good, but success message block handles redirection message
            // this.showToast('Success', 'Disbursement Request submitted successfully!', 'success');
        } catch (error) {
            console.error('Submission Error:', error);
            this.errorMessageText = this.getErrorMessage(error);
            this.showToast('Error Submitting Request', this.errorMessageText, 'error');
        } finally {
            this.isSubmitting = false;
            console.log('handleSubmit finished');
        }
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({ title, message, variant });
        this.dispatchEvent(event);
    }

    getErrorMessage(error) {
        let message = 'Unknown error';
        if (error) {
            if (error.body) {
                if (Array.isArray(error.body)) {
                    message = error.body.map(e => e.message).join(', ');
                } else if (typeof error.body.message === 'string') {
                    message = error.body.message;
                } else if (error.body.pageErrors && error.body.pageErrors.length > 0) {
                    message = error.body.pageErrors[0].message;
                } else if (error.body.fieldErrors) {
                    const fieldErrors = [];
                    for (const fieldName in error.body.fieldErrors) {
                        if (Object.hasOwnProperty.call(error.body.fieldErrors, fieldName)) {
                            error.body.fieldErrors[fieldName].forEach(err => {
                                fieldErrors.push(`${fieldName}: ${err.message}`);
                            });
                        }
                    }
                    message = fieldErrors.join('; ');
                } else if (error.body.message) { // Fallback for general body message
                     message = error.body.message;
                }
            } else if (error.message) {
                message = error.message;
            }
        }
        return message;
    }
}
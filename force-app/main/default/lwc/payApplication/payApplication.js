// /* eslint-disable */
// import { LightningElement, api } from "lwc";

// export default class PayApplication extends LightningElement {
//   /** Raw lists passed in from parent; could be arrays or single objects. */
//   @api payApplications = [];
//   @api transactions = [];
//   @api projectData = [];
//   @api cashFlowData = [];
//   @api weekColumns = [];

//   connectedCallback() {
//     console.log("API  payApplications:", JSON.stringify(this.payApplications));
//     console.log("API  transactions:", JSON.stringify(this.transactions));
//     console.log("API  projectData:", JSON.stringify(this.projectData));
//     console.log("API  cashFlowData:", JSON.stringify(this.cashFlowData));
//     console.log('weekColumns:  ', JSON.stringify(this.weekColumns));

//     // Perform the week-by-week aggregation and patch the "PayAppSubmitted" node
//     this.updatePayAppData();

//     // Log the final structure for verification
//     console.log(
//       "Updated cashFlowData:",
//       JSON.stringify(this.cashFlowData, null, 2)
//     );

//     // Also log whatever your existing calculatorJson is
//     console.log(
//       "Generated PayApplication JSON:",
//       JSON.stringify(this.calculatorJson, null, 2)
//     );
//   }

  
//   formatDate(isoString) {
//     if (!isoString) {
//       return "";
//     }
//     const dt = new Date(isoString);
//     const year = dt.getFullYear();
//     const month = `0${dt.getMonth() + 1}`.slice(-2);
//     const day = `0${dt.getDate()}`.slice(-2);
//     return `${month}/${day}/${year}`;
//   }

  
//   getNextFriday(date) {
//     const d = new Date(date);
//     const dow = d.getDay();
//     const offset = (5 - dow + 7) % 7;
//     d.setDate(d.getDate() + offset);
//     d.setHours(0, 0, 0, 0);
//     return d;
//   }

 
  

//   updatePayAppData() {
//   // 1a) Normalize inputs to arrays
//   const payApps = Array.isArray(this.payApplications)
//     ? this.payApplications
//     : this.payApplications
//     ? [this.payApplications]
//     : [];

//   const txns = Array.isArray(this.transactions)
//     ? this.transactions
//     : this.transactions
//     ? [this.transactions]
//     : [];

//   const projArray = Array.isArray(this.projectData)
//     ? this.projectData
//     : this.projectData
//     ? [this.projectData]
//     : [];

//   // 1b) Sort transactions by CreatedDate ascending
//   const sortedTxns = txns
//     .slice()
//     .sort((a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate));

//   // 1c) Build per-transaction info, including grossPayApp = projectedPayment × (1 – retainageFrac)
//   const perTxn = sortedTxns.map((tx) => {
//     const payAppId = tx.Related_Pay_Application__c;
//     let rawProjectedPayment = 0;
//     if (payAppId) {
//       const pa = payApps.find((p) => p.Id === payAppId);
//       if (pa && typeof pa.Projected_payment__c === "number") {
//         rawProjectedPayment = pa.Projected_payment__c;
//       }
//     }

//     const projectId = tx.Project__c;
//     const proj = projArray.find((p) => p.Id === projectId) || {};
//     const rawRetainage =
//       typeof proj.Retainage__c === "number" ? proj.Retainage__c : 0;
//     const retainageFrac = rawRetainage / 100;

//     const grossPayApp = rawProjectedPayment * (1 - retainageFrac);
//     const weekKeyDate = this.getNextFriday(new Date(tx.CreatedDate));

//     return {
//       weekKey: weekKeyDate.getTime(),
//       weekDate: weekKeyDate,
//       payAppId,
//       grossPayApp,
//       projectedPayment: rawProjectedPayment
//     };
//   });

//   // 1d) Group by weekKey → { weekDate, grossSum, projectedSum, items[] }
//   const weeklyMap = perTxn.reduce((acc, obj) => {
//     const key = obj.weekKey;
//     if (!acc[key]) {
//       acc[key] = {
//         weekDate: obj.weekDate,
//         grossSum: 0,
//         projectedSum: 0,
//         items: []
//       };
//     }
//     acc[key].grossSum += obj.grossPayApp;
//     acc[key].projectedSum += obj.projectedPayment;
//     acc[key].items.push({
//       id: obj.payAppId,
//       grossPayApp: obj.grossPayApp,
//       projectedPayment: obj.projectedPayment
//     });
//     return acc;
//   }, {});

//   // 2) Build a new cashFlowData array, but replace rows at three IDs
//   const newCashFlowData = this.cashFlowData.map((row) => {
//     if (
//       row.id !== "ProjectedNetPayApp" &&
//       row.id !== "PayAppSubmitted" &&
//       row.id !== "LessRetainage"
//     ) {
//       return row;
//     }

//     // Deep-clone the row and its weeks[]
//     const clonedWeeks = Array.isArray(row.weeks)
//       ? row.weeks.map((w) => ({ ...w }))
//       : [];
//     const clonedRow = {
//       ...row,
//       weeks: clonedWeeks
//     };

//     // For each week-group, find matching week in clonedWeeks
//     Object.values(weeklyMap).forEach((grp) => {
//       const year = grp.weekDate.getFullYear();
//       const month = String(grp.weekDate.getMonth() + 1).padStart(2, "0");
//       const day = String(grp.weekDate.getDate()).padStart(2, "0");
//       const isoDate = `${year}-${month}-${day}`;

//       const matchWeek = clonedWeeks.find((wk) => wk.date === isoDate);
//       if (!matchWeek) return;

//       // 2a) ProjectedNetPayApp
//       if (row.id === "ProjectedNetPayApp") {
//         matchWeek.value = grp.grossSum;
//         matchWeek.projectedValue = grp.projectedSum;
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           grossPayApp: item.grossPayApp,
//           projectedPayment: item.projectedPayment
//         }));
//       }
//       // 2b) PayAppSubmitted
//       else if (row.id === "PayAppSubmitted") {
//         matchWeek.value = grp.projectedSum;
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           projectedPayment: item.projectedPayment
//         }));
//       }
//       // 2c) LessRetainage
//       else if (row.id === "LessRetainage") {
//         // total retainage = projectedSum – grossSum
//         const retainageTotal = grp.projectedSum - grp.grossSum;
//         // store as a negative value
//         matchWeek.value = -retainageTotal;
//         // (optional) if a popover is desired, could add dateLineItem; otherwise skip
//         matchWeek.dateLineItem = grp.items.map((item) => ({
//           id: item.id,
//           // compute each item’s retainage if needed:
//           // retainage: item.projectedPayment - item.grossPayApp
//           retainage: item.projectedPayment - item.grossPayApp
//         }));
//       }
//     });

//     return clonedRow;
//   });

//   // 3) Reassign to trigger LWC reactivity
//   this.cashFlowData = newCashFlowData;

//   // 4) Dispatch event if parent needs to know
//   this.dispatchEvent(
//     new CustomEvent("cashflowdataupdate", {
//       detail: this.cashFlowData,
//       bubbles: true,
//       composed: true
//     })
//   );
// }



//   /**
//    * Build “weekly-aggregated” rows for the Pay Application tab.
//    * (This is your existing displayRows logic, unchanged.)
//    */
  


//   get displayRows() {
//     // 1) Normalize inputs to arrays
//     const payApps = Array.isArray(this.payApplications)
//       ? this.payApplications
//       : this.payApplications
//       ? [this.payApplications]
//       : [];

//     const txns = Array.isArray(this.transactions)
//       ? this.transactions
//       : this.transactions
//       ? [this.transactions]
//       : [];

//     const projArray = Array.isArray(this.projectData)
//       ? this.projectData
//       : this.projectData
//       ? [this.projectData]
//       : [];

//     // 2) Build groupsByISO: { "YYYY-MM-DD" → { grossSum, appliedSum } }
//     //    where appliedSum = grossPayApp × (percentToLoan)
//     const groupsByISO = txns.reduce((acc, tx) => {
//       // a) Find related PayApplication and Project for this txn
//       const payAppId = tx.Related_Pay_Application__c;
//       let projectedPayment = 0;
//       let rawPercentToLoan = 0;
//       if (payAppId) {
//         const pa = payApps.find((p) => p.Id === payAppId);
//         if (pa) {
//           projectedPayment =
//             typeof pa.Projected_payment__c === "number"
//               ? pa.Projected_payment__c
//               : 0;
//           rawPercentToLoan =
//             typeof pa.of_Pay_App_to_Loan__c === "number"
//               ? pa.of_Pay_App_to_Loan__c
//               : 0;
//         }
//       }

//       const projectId = tx.Project__c;
//       const proj = projArray.find((p) => p.Id === projectId) || {};
//       const rawRetainage =
//         typeof proj.Retainage__c === "number" ? proj.Retainage__c : 0;
//       const retainageFrac = rawRetainage / 100; // e.g. 5% → 0.05
//       const percentFrac = rawPercentToLoan / 100; // e.g. 20% → 0.20

//       // b) Compute grossPayApp and appliedToMfLoan
//       const grossPayApp = projectedPayment * (1 - retainageFrac);
//       const appliedToMfLoan = grossPayApp * percentFrac;

//       // c) Find this txn’s “Friday ISO” key
//       const weekFriDate = this.getNextFriday(new Date(tx.CreatedDate));
//       const yyyy = weekFriDate.getFullYear();
//       const mm = String(weekFriDate.getMonth() + 1).padStart(2, "0");
//       const dd = String(weekFriDate.getDate()).padStart(2, "0");
//       const iso = `${yyyy}-${mm}-${dd}`; // e.g. "2025-06-06"

//       // d) Accumulate sums
//       if (!acc[iso]) {
//         acc[iso] = { grossSum: 0, appliedSum: 0 };
//       }
//       acc[iso].grossSum += grossPayApp;
//       acc[iso].appliedSum += appliedToMfLoan;

//       return acc;
//     }, {});

//     // 3) Now iterate exactly over weekColumns to produce one row per weekColumn.
//     //    If groupsByISO[isoDate] is missing, we default grossSum/appliedSum to 0.
//     const rows = [];
//     this.weekColumns.forEach((wkCol, index) => {
//       const isoDate = wkCol.date; // must be "YYYY-MM-DD"
//       const sums = groupsByISO[isoDate] || { grossSum: 0, appliedSum: 0 };

//       const gross = sums.grossSum;
//       const applied = sums.appliedSum;
//       const percentToMfLoan = gross > 0 ? (applied / gross) * 100 : 0;

//       rows.push({
//         id: isoDate, // or isoDate + "-" + index if you need a guaranteed unique key
//         date: new Date(isoDate).toLocaleDateString(),
//         week: index + 1, // “1” for the first entry in weekColumns, “2” for the second, etc.
//         grossPayApp: `$ ${gross.toFixed(2)}`,
//         percentToMfLoan: `${percentToMfLoan.toFixed(2)}%`,
//         appliedToMfLoan: `$ ${applied.toFixed(2)}`,
//       });
//     });

//     return rows;
//   }

//   /**
//    * Build the JSON structure for only the PayApplication tab:
//    * {
//    *   cashflowPayAppCalculatorData: [
//    *     {
//    *       id: "1",
//    *       tabName: "PayApplication",
//    *       weeks: [ … output of displayRows … ]
//    *     }
//    *   ]
//    * }
//    */
//   get calculatorJson() {
//     const payAppWeeks = this.displayRows;
//     return {
//       cashflowPayAppCalculatorData: [
//         {
//           id: "1",
//           tabName: "PayApplication",
//           weeks: payAppWeeks
//         }
//       ]
//     };
//   }

//   /**
//    * Expose a ready-made JSON string if needed:
//    *   JSON.stringify(this.calculatorJson, null, 2)
//    */
//   get calculatorJsonString() {
//     return JSON.stringify(this.calculatorJson, null, 2);
//   }
// }


import { LightningElement, api } from 'lwc';

export default class PayApplication extends LightningElement {
  @api cashFlowData = [];

  connectedCallback() {
    console.log('API cashFlowData:', JSON.stringify(this.cashFlowData));
  }

  get displayRows() {
    // grab the two source rows
    const projNode = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
    const pctNode  = this.cashFlowData.find(r => r.id === 'payAppAppliedmfloan');

    // normalize to arrays
    const projWeeks = Array.isArray(projNode?.weeks) ? [...projNode.weeks] : [];
    const pctWeeks  = Array.isArray(pctNode?.weeks)  ? [...pctNode.weeks]  : [];

    // sort by date ascending
    projWeeks.sort((a, b) => new Date(a.date) - new Date(b.date));
    pctWeeks .sort((a, b) => new Date(a.date) - new Date(b.date));

    return projWeeks.map(pw => {
      const date       = pw.date;
      const gross      = typeof pw.value === 'number' ? pw.value : 0;
      const pctEntry   = pctWeeks.find(wk => wk.date === date);
      const pct        = pctEntry && typeof pctEntry.value === 'number' ? pctEntry.value : 0;
      const applied    = gross * (pct / 100);

      return {
        id: pw.id,
        date: new Date(date).toLocaleDateString(),
        grossPayApp:    `$ ${gross.toFixed(2)}`,
        percentToMfLoan:`${pct.toFixed(2)}%`,
        appliedToMfLoan:`$ ${applied.toFixed(2)}`
      };
    });
  }
}
/* eslint-disable */
import { api, LightningElement,track } from 'lwc';

export default class GeneralLoanSummary extends LightningElement {

    
    @api projectNameProp;
    @api accountData = [];
    @api weekColumns = [];
    @api cashFlowData = [];
    @api projectData = [];
    @api transactions = [];
    @api activeCashflow = [];
    @track isLoading = true;
    @track servicerNotes = ' Another RIGID loan for JAG Financial. They are purchasing material. <PERSON><PERSON> is anticipated to be repaid end of June; will function the same as JAG 6.2. Total amount of payapp may not be received to MF due to nature of how JAG/Cap+ work with their customer. I have plugged in the anticipated amount of payment to Cap+/JAG from Rigid, which will flow to MF-JAG account for repayment. Please see the "JAG INT" tab for calculations. I am using 70-days in Project CF tab. Any balance(s) remaining will need to be paid by JAG/Cap+ or they will need to add additional receivables from their customers in for repayment. Any questions please ask: <PERSON><PERSON> or <PERSON>'
    
    scopeWorkMapping = [
        { label: 'Direct Employee Labor Cost', id: 'expense-DirectEmployeeLaborCost', mfLoanPerItem: 0 },
        { label: 'Subcontract Labor',          id: 'expense-SubcontractLabor', mfLoanPerItem: 0 },
        { label: 'Material',                   id: 'expense-Material',           mfLoanPerItem: 0 },
        { label: 'Equipment Rental',           id: 'expense-EquipmentRental',    mfLoanPerItem: 0 },
        { label: 'Bond Premium (if applicable)',           id: 'expense-BondPremium',    mfLoanPerItem: 0 },
        { label: 'Misc.',           id: 'expense-MiscellaneousExpense',    mfLoanPerItem: 0 },
        
    ];


  connectedCallback(){
    
    console.log('projectNameProp: ' + this.projectNameProp);
    console.log('account data: ' + JSON.stringify(this.accountData));
    console.log('loan week columns: ' + JSON.stringify(this.weekColumns));
    console.log('cashflow data: ' + JSON.stringify(this.cashFlowData));
    console.log('project data: ' + JSON.stringify(this.projectData));
    console.log('transactions: ' + JSON.stringify(this.transactions));
    console.log('active cashflow: ' + JSON.stringify(this.activeCashflow));
  }

  getCashFlowTotal(nodeId) {
    const node = this.cashFlowData.find(n => n.id === nodeId);
    return node ? node.calculatedTotal : 0;
  }

  get scopeWorkData() {
    const rows =  this.scopeWorkMapping.map(item => ({
      label:          item.label,
      projectCost:    this.getCashFlowTotal(item.id),
      mfLoanPerItem:  item.mfLoanPerItem
    }));
    
     // now append Profit (Margin) row, computed specially
    const profitValue = this.projectGrossMargin - Math.abs(this.lessRetainage);
    rows.push({
      label:          'Profit (Margin)',
      projectCost:    profitValue,
      mfLoanPerItem:  0
    });

    return rows;

  }

//   get customerName(){
//     return this.customerName;
//   }

  get customerName(){
    return this.accountData.Name;
  }

   get projectName(){
    return this.projectNameProp;
   }

   get approvedMFLoan(){
    return this.projectData.Loan_Principal__c;
   }

    get projectGrossMargin() {
        const netNode  = this.cashFlowData.find(n => n.id === 'ProjectedNetPayApp');
        const costNode = this.cashFlowData.find(n => n.id === 'TotalProjectCost');
        const net  = netNode  ? netNode.calculatedTotal  : 0;
        const cost = costNode ? costNode.calculatedTotal : 0;
        return net - cost;
    }

    get grossContractValue(){
        const contractNode  = this.cashFlowData.find(n => n.id === 'PayAppSubmitted');
        return contractNode ? contractNode.calculatedTotal : 0;
    }
    get lessRetainage(){
        const retainedNode  = this.cashFlowData.find(n => n.id === 'LessRetainage');
        return retainedNode ? retainedNode.calculatedTotal : 0;
    }

    get grossLTV(){
        const loan = this.approvedMFLoan;
        const contract = this.grossContractValue;
        return loan ? (loan / contract) * 100 : 0;
    }

    get loanDate(){
        return this.activeCashflow.Forecast_Start_Date__c;
    }

    get loanMaturityDate(){
        return this.activeCashflow.Forecast_End_Date__c;
    }


    get weeksOutstanding() {
        return Array.isArray(this.weekColumns)
            ? this.weekColumns.length
            : 0;
    }

    get projectId(){
        return this.projectData.Id;
    }

    get mfCashOut() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => sum + (tx.Loan_Principal_to_date__c || 0), 0);
    }

    get originationFee() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => {
                return sum +
                    (tx.Default_Fee_Application__c || 0) +
                    (tx.Doc_Stamp_Fees_Application__c || 0) +
                    (tx.Late_Fee_Application__c || 0) +
                    (tx.Legal_Fees_Application__c || 0);
            }, 0);
    }

    get interestIncome() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => sum + (tx.Interest_Application__c || 0), 0);
    }

    get totalIncome() {
        return this.originationFee + this.interestIncome;
    }

    get totalPrincipalInterest() {
        return this.mfCashOut + this.totalIncome;
    }



    handleServicerNotesChange(event) {
        this.servicerNotes = event.target.value;
    }

     // New Doc-Stamp fields
    @track docStampsRequired = 'No';   // bind this to your “Required?” display (or change to YES if needed)
    @track unionLabor        = 'No';   // bind this to your “Union Labor?” display

    get yesNoOptions() {
      return [
        { label: 'Yes', value: 'YES' },
        { label: 'No',  value: 'NO'  }
      ];
    }

    // handlers for combobox changes
    // handleDocStampsRequiredChange(event) {
    //   this.docStampsRequired = event.detail.value;
    // }
    // handleUnionLaborChange(event) {
    //   this.unionLabor = event.detail.value;
    // }


    get totalProjectCost() {
        return this.scopeWorkData.reduce((sum, row) => sum + row.projectCost, 0);
    }

    get totalMfLoan() {
        return this.scopeWorkData.reduce((sum, row) => sum + row.mfLoanPerItem, 0);
    }

    get docStampAmount() {
      if (this.docStampsRequired === 'YES') {
        const raw = this.totalMfLoan * 0.0035;
        const capped = raw >= 2450 ? 2450 : raw;
        // round to cents
        return parseFloat(capped.toFixed(2));
      }
      return 0;
    }

    handleDocStampsRequiredChange(evt) {
      this.docStampsRequired = evt.detail.value; // or evt.target.checked ? 'YES' : 'No'
    }
    handleUnionLaborChange(evt) {
      this.unionLabor = evt.detail.value;
    }


    get mfDisbursmentWeeks() {
        const row = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
        return row ? row.weeks : [];
    }

    // 2.5) “PayApp” weeks (for the middle bottom table)
    get PayAppWeeks() {
        const row = this.cashFlowData.find(r => r.id === 'revenue-PayApplicationtobeSubmitted');
        return row ? row.weeks : [];
    }

    get mfLoanDisbursementTotal() {
        return this.mfDisbursmentWeeks
            .reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0)
            .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    get payAppTotal() {
        return this.PayAppWeeks
            .reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0)
            .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
}
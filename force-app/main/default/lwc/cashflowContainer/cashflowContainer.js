/* eslint-disable */
import { LightningElement, api, track, wire } from 'lwc';
import { CurrentPageReference } from 'lightning/navigation';
import getCashflowData from '@salesforce/apex/CashflowDataService.getCashflowData';
// import { calculateLoanDisbursementCashFlow, calculatePayApplicationCashFlow } from 'c/cashFlowCalculations';
import saveCashflowDetails from '@salesforce/apex/CashflowDataService.saveCashflowDetails';
import saveAsCashflow from '@salesforce/apex/CashflowDataService.saveAsCashflow';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import uploadAndParseExcel from '@salesforce/apex/CashflowDataService.uploadAndParseExcel';
import CASHFLOW_LINE_ITEM_OBJECT from '@salesforce/schema/Cashflow_Line_Item__c';
import { getObjectInfo, getPicklistValues } from 'lightning/uiObjectInfoApi';
import CATEGORY_FIELD from '@salesforce/schema/Cashflow_Line_Item__c.Line_Item_Category__c';
import { NavigationMixin } from 'lightning/navigation';
// --- Constants ---
const VARIABLE_FIXED_OPTIONS = [{ label: 'Variable', value: 'Variable' }, { label: 'Fixed', value: 'Fixed' }];
const PAYMENT_FREQUENCY_OPTIONS = [{ label: 'Weekly', value: 'Weekly' }, { label: 'Bi-Weekly', value: 'Bi-Weekly' }, { label: 'Monthly', value: 'Monthly' }, { label: 'Annually', value: 'Annually' }];
const POPOVER_ROW_TYPES = ['DATA_EXPENSE', 'DATA_FINANCING_EDITABLE', 'DATA_REVENUE_EDITABLE', 'DATA_READONLY']; //DATA_READONLY for completeness, though popover might be disabled
const CALCULATED_ROW_TYPES = ['DATA_CALCULATED_TOTAL', 'DATA_CALCULATED_SUMMARY'];
const APP_HEADER_TABS = {
    CASH_FLOW: 'appCashFlow', COVER_PAGE_WRITE_UP: 'coverPageWriteUp', CALCULATOR: 'Calculator', DEAL_SUMMARY: 'dealSummary',
    CONTRACT_AND_PROJECT_INFO: 'contractAndProjectInfo', COMMITTEE_FLOW: 'committeeFlow', FACTOR_DETAILS: 'factorDetails',
    AR_TRACKER: 'arTracker', AR_CHART: 'arChart', HANDOFF: 'handoff', TERM_SHEET: 'termSheet', OTHER: 'demoTab'
};
// Assumed relationship name from Cashflow_Line_Item__c to Cashflow_Line_Item_Detail__c
const CHILD_LINE_ITEMS_REL_NAME = 'Cashflow_Line_Item_Details__r';


const createSectionHeader = (label, showAdd = false) => {
    const id = `section-${label.replace(/\s+/g, '').toLowerCase()}`;
    return {
        id, label, isSectionHeader: true, sectionId: id, showAddButton: showAdd,
        type: 'SECTION_HEADER', level: 0, weeks: [], calculatedTotal: null, rowClass: 'slds-text-title_caps section-header'
    };
};

// --- Sample Data Generation Functions (for fallback scenarios) ---
// These remain largely the same as they are for fallback UI structure.
// The internal structure of a cell (e.g., cell.originalLineItems) will be different with real data.
const createSampleWeekColumns = () => { /* ... (same as provided) ... */
    let columns = [];
    // const weeks = this.selectedWeeks?this.selectedWeeks:'0';
    // console.log('selected weeks'+weeks);
    for (let i = 1; i <= 12; i++) {
        const date = new Date(2025, 5, (i * 7) - 6); // Example: First 12 Mondays in June 2025
        columns.push({
            id: `weekSample${i}`, label: `W${i}`, date: date.toISOString().split('T')[0],
            formattedDate: `${date.getMonth() + 1}/${date.getDate()}/${String(date.getFullYear()).slice(-2)}`
        });
    }
    return columns;
};
const createSampleDataRow = (rowId, label, type, level, parentId, weekColumns, baseValue, isDifference) => { /* ... (same as provided, but note cell.originalLineItems would be child details) ... */
    let weeks = []; let total = 0;
    weekColumns.forEach((col, index) => {
        const hasPopover = POPOVER_ROW_TYPES.includes(type);
        const weekValue = parseFloat((baseValue * (1 + (Math.random() - 0.5) * 0.1)).toFixed(2));
        let cell = {
            id: `cell-${rowId}-${index}`, weekIdentifier: col.id, value: weekValue, weekLabel: col.label, rowLabel: label, cellClass: 'slds-text-align_center', hasPopover: hasPopover,
            category: hasPopover ? `${label} Line Item` : null,
            expenseCategory: hasPopover && type === 'DATA_EXPENSE' ? label : null,
            date: hasPopover ? col.date : null,
            variableFixed: hasPopover ? (Math.random() > 0.5 ? 'Variable' : 'Fixed') : null,
            paymentFrequency: hasPopover ? PAYMENT_FREQUENCY_OPTIONS[Math.floor(Math.random() * PAYMENT_FREQUENCY_OPTIONS.length)].value : null,
            weeklyAmount: hasPopover ? weekValue : null,
            paymentTerm: hasPopover ? (Math.random() > 0.3 ? 'Net 30' : 'Net 60') : null,
            isDifference: isDifference,
            salesforceIds: [], // Would hold parent Cashflow_Line_Item__c Id
            originalLineItems: [] // Would hold child Cashflow_Line_Item_Detail__c records
        };
        if (hasPopover) cell.cellClass += ' cell-clickable';
        if (cell.isDifference) cell.cellClass += ' cell-difference wholelightblue';
        weeks.push(cell); total += cell.value || 0;
    });
    let rowClass = 'data-row'; let indentStyle = `padding-left: ${level * 1.5}rem;`;
    if (CALCULATED_ROW_TYPES.includes(type)) rowClass += ' calculated-row';
    if (type === 'DATA_CALCULATED_SUMMARY') rowClass += ' summary-row';
    return {
        id: rowId, label: label, isSectionHeader: false, isEditable: POPOVER_ROW_TYPES.includes(type), type: type, level: level, parentId: parentId, weeks: weeks,
        calculatedTotal: parseFloat(total.toFixed(2)), rowClass: rowClass, indentStyle: indentStyle
    };
};
const generateSampleCashflowData = () => { /* ... (same as provided) ... */
    const columns = createSampleWeekColumns();
    const rows = [
        createSectionHeader('PROJECT REVENUE'),
        createSampleDataRow('PayAppSubmitted', 'Pay App to be Submitted', 'DATA_REVENUE_EDITABLE', 1, null, columns, 100000, true),
        createSampleDataRow('LessRetainage', 'Less Retainage', 'DATA_REVENUE_EDITABLE', 1, null, columns, -5000, true),
        createSampleDataRow('ProjectedNetPayApp', 'Projected Net Pay App', 'DATA_CALCULATED_TOTAL', 1, null, columns, 0, false),
        createSectionHeader('PROJECT COSTS', true),
        createSampleDataRow('ExpenseCat1', 'Material', 'DATA_EXPENSE', 1, null, columns, 20000, true),
        createSampleDataRow('ExpenseCat2', 'Labor', 'DATA_EXPENSE', 1, null, columns, 50000, true),
        createSampleDataRow('TotalProjectCost', 'Total Project Cost', 'DATA_CALCULATED_TOTAL', 1, null, columns, 0, false),
        createSectionHeader('FINANCING SOURCES'),
        createSampleDataRow('MFLoanDisb', 'MF Loan Disbursement', 'DATA_FINANCING_EDITABLE', 1, null, columns, 15000, true),
        createSampleDataRow('ReceiptPayApp', 'Receipt of Pay App', 'DATA_READONLY', 1, null, columns, 0, true),
        createSampleDataRow('TotalSources', 'Total Sources of Cash', 'DATA_CALCULATED_TOTAL', 1, null, columns, 0, false),
        createSectionHeader('USES'),
        createSampleDataRow('MFLoanRepay', 'MF Loan Repayment', 'DATA_FINANCING_EDITABLE', 1, null, columns, 1000, true),
        createSampleDataRow('ProjectCostsPaid', 'Project Costs Paid That Week', 'DATA_FINANCING_EDITABLE', 1, null, columns, 0, true),
        createSampleDataRow('otherSSV', 'OTHER SSV PAYMENTS REQ\'d', 'DATA_FINANCING_EDITABLE', 1, null, columns, 400, true),
        createSampleDataRow('alternativeLoanPayment', 'Alternative Loan Payment', 'DATA_FINANCING_EDITABLE', 1, null, columns, 230, true),
        createSampleDataRow('TotalUses', 'Total Uses of Cash', 'DATA_CALCULATED_TOTAL', 1, null, columns, 0, false),
        createSectionHeader('CASH FLOW SUMMARY'),
        createSampleDataRow('NetWeeklyCashFlow', 'Net Weekly Cash Flow', 'DATA_CALCULATED_SUMMARY', 1, null, columns, 0, false),
        createSampleDataRow('AccumulatedSurplus', 'Accumulated Surplus/(Deficit)', 'DATA_CALCULATED_SUMMARY', 1, null, columns, 0, false)
    ];
    return { projectName: 'Sample Project (Fallback)', weekColumns: columns, rows: rows };
};
const generateSampleCoverData = () => { /* ... (same as provided) ... */
    return { projectOverview: { name: 'Sample Project', client: 'Sample Client' }, keyMetrics: [], recommendation: 'Sample Recommendation' };
};
const generateSampleCalculatorData = () => { /* ... (same as provided) ... */
    return { loanDetails: [], summary: {} };
};


export default class CashflowContainer extends NavigationMixin(LightningElement) {
    @api recordId;

    @track activeAppHeaderTab = APP_HEADER_TABS.CASH_FLOW;

    @track projectName = 'Loading Project...';
    @track weekColumns = [];
    @track cashFlowData = [];
    @track coverPageData = {};
    @track calculatorData = {};
    @track customerName = '';


    @track disbursementData = [];
    @track payApplicationData = [];
    @track transactionData = [];
    @track projectData = [];

    @track isLoading = true;
    @track error;
    isLoadingForInitialLoad = false;

    @track selectedView = 'Underwriting';
    @track selectedVersion = '';
    @track selectedWeeks = 52;

    viewOptions = [ /* ... */];
    @track versionOptions = [];
    weeksOptions = [ /* ... */];

    @track uploadModalOpen = false;
    @track saveAs = false;

    file;
    variableFixedOptions = VARIABLE_FIXED_OPTIONS;
    paymentFrequencyOptions = PAYMENT_FREQUENCY_OPTIONS;

    _projectData;
    _activeCashflow;
    _forecastLines; // Will store parent Cashflow_Line_Item__c records, which include their children
    _initialForecastLinesWithChildren = []; // For diffing children on save
    _transactions;
    _account;

    _currentProjectId;
    _currentCashflowId;
    _urlCashflowId;
    @track expenseCategoryOptions = [];
    @wire(CurrentPageReference)
    pageRef;

    // --- Getters for App Header Tab styling & active tab boolean (same as provided) ---
    get isCoverWriteUpActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.COVER_PAGE_WRITE_UP; }
    get isProjectCashflowsActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.CASH_FLOW; }
    get isCalculatorActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.CALCULATOR; }
    get isDealSummaryActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.DEAL_SUMMARY; }
    get isContractProjectInfoActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.CONTRACT_AND_PROJECT_INFO; }
    get isCommitteeFlowActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.COMMITTEE_FLOW; }
    get isFactorDetailsActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.FACTOR_DETAILS; }
    get isArTrackerActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.AR_TRACKER; }
    get isArChartActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.AR_CHART; }
    get isHandoffActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.HANDOFF; }
    get isTermSheetActive() { return this.activeAppHeaderTab === APP_HEADER_TABS.TERM_SHEET; }
    get isdemoTab() { return this.activeAppHeaderTab === APP_HEADER_TABS.OTHER; }

    get appHeaderTabCashFlow() { return this.activeAppHeaderTab === APP_HEADER_TABS.CASH_FLOW ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabCoverPageWriteUp() { return this.activeAppHeaderTab === APP_HEADER_TABS.COVER_PAGE_WRITE_UP ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabCalculator() { return this.activeAppHeaderTab === APP_HEADER_TABS.CALCULATOR ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    // ... other tab style getters ...
    get appHeaderTabDealSummary() { return this.activeAppHeaderTab === APP_HEADER_TABS.DEAL_SUMMARY ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabContractProjectInfo() { return this.activeAppHeaderTab === APP_HEADER_TABS.CONTRACT_AND_PROJECT_INFO ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabCommitteeFlow() { return this.activeAppHeaderTab === APP_HEADER_TABS.COMMITTEE_FLOW ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabFactorDetails() { return this.activeAppHeaderTab === APP_HEADER_TABS.FACTOR_DETAILS ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabArTracker() { return this.activeAppHeaderTab === APP_HEADER_TABS.AR_TRACKER ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabArChart() { return this.activeAppHeaderTab === APP_HEADER_TABS.AR_CHART ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabHandoff() { return this.activeAppHeaderTab === APP_HEADER_TABS.HANDOFF ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabTermSheet() { return this.activeAppHeaderTab === APP_HEADER_TABS.TERM_SHEET ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }
    get appHeaderTabOther() { return this.activeAppHeaderTab === APP_HEADER_TABS.OTHER ? 'slds-tabs_default__item slds-is-active selected-tab' : 'slds-tabs_default__item'; }


    // connectedCallback() {
    //     this.template.addEventListener('toasterror', this.handleChildToastError.bind(this));
    // }

    // renderedCallback() {
    //     // ... (same as provided, calls loadCashflowDetails) ...
    //     if (this.pageRef && !this.isLoadingForInitialLoad && !this._currentProjectId) {
    //         this.isLoadingForInitialLoad = true;
    //         const state = this.pageRef.state;
    //         this._urlCashflowId = state?.c__recordId;
    //         this._currentProjectId = state?.c__projectId || this.recordId;

    //         console.log('[CashflowContainer] PageRef State:', JSON.stringify(state));
    //         console.log('[CashflowContainer] URL Cashflow ID (c__recordId):', this._urlCashflowId);
    //         console.log('[CashflowContainer] Resolved Project ID (c__projectId or @api recordId):', this._currentProjectId);

    //         if (this._currentProjectId) {
    //             console.log('getting cashflow');
    //             this.loadCashflowDetails(this._currentProjectId, this._urlCashflowId);
    //         } else {
    //             this.error = 'Project Record ID is not available.';
    //             this.isLoading = false;
    //             this.showToast('Warning', this.error, 'warning');
    //             // this.loadSampleDataForFallback();
    //         }
    //     } else if (!this._currentProjectId && !this.recordId && !this.pageRef && !this.isLoadingForInitialLoad) {
    //         this.isLoadingForInitialLoad = true;
    //         this.error = 'No Project ID context found.';
    //         this.isLoading = false;
    //         this.showToast('Warning', this.error, 'warning');
    //         // this.loadSampleDataForFallback();
    //     }
    // }

    connectedCallback() {
        this.template.addEventListener('toasterror', this.handleChildToastError.bind(this));
        setTimeout(() => {
            if (!this.isLoadingForInitialLoad) {
                this.isLoadingForInitialLoad = true;

                const state = this.pageRef?.state || {};
                this._urlCashflowId = state.c__recordId;
                this._currentProjectId = state.c__projectId || this.recordId;

                console.log('[CashflowContainer] PageRef State:', JSON.stringify(state));
                console.log('[CashflowContainer] URL Cashflow ID (c__recordId):', this._urlCashflowId);
                console.log('[CashflowContainer] Resolved Project ID (c__projectId or @api recordId):', this._currentProjectId);

                if (this._currentProjectId) {
                    console.log('getting cashflow');
                    this.loadCashflowDetails(this._currentProjectId, this._urlCashflowId);
                } else {
                    this.error = 'Project Record ID is not available.';
                    this.isLoading = false;
                    this.showToast('Warning', this.error, 'warning');
                   
                }
            }
        }, 100); 
    }


    async loadCashflowDetails(projectIdToLoad, cashflowIdFromUrl) {
        this.isLoading = true;
        this.error = undefined;
        console.log(`[CashflowContainer] loadCashflowDetails called with projectId: ${projectIdToLoad}, cashflowIdFromUrl: ${cashflowIdFromUrl}`);
        try {

            const pageData = await getCashflowData({ projectId: projectIdToLoad, cashflowId: cashflowIdFromUrl });
            console.log('[CashflowContainer] Fetched Page Data from Apex:', JSON.stringify(pageData));

            if (pageData) {
                console.log('enter pageData ' + JSON.stringify(pageData));
                this._projectData = pageData.project;
                console.log('project data ' + JSON.stringify(this._projectData));
                this._activeCashflow = pageData.activeCashflow;
                console.log('cashflow : ', JSON.stringify(this._activeCashflow));
                //this.selectedWeeks = (pageData.activeCashflow && pageData.activeCashflow.Projected_Weeks_Outstanding__c) ? pageData.activeCashflow.Projected_Weeks_Outstanding__c : 52;
                this.selectedWeeks = (pageData.activeCashflow &&
                    pageData.activeCashflow.Projected_Weeks_Outstanding__c)
                    || 52;

                console.log('selected week ', this.selectedWeeks);
                this._forecastLines = pageData.forecastLines || []; // These are parent CLIs with children nested
                this._initialForecastLinesWithChildren = JSON.parse(JSON.stringify(this._forecastLines)); // Deep copy for diffing later
                this._transactions = pageData.transactions || [];
                this._currentCashflowId = this._activeCashflow ? this._activeCashflow.Id : null;

                console.log('[CashflowContainer] Effective Cashflow ID loaded:', this._currentCashflowId);
                console.log('[CashflowContainer] Raw _forecastLines (parents with children):', JSON.parse(JSON.stringify(this._forecastLines)));

                this.customerName = this._account ? this._account.Name : 'Account Not Found';
                
                console.log('customerName ' + this.customerName);
                this.projectName = this._projectData ? this._projectData.Name : 'Project Data Not Found';
                console.log('projecName ' + this.projectName);
                this.weekColumns = this.generateWeekColumnsFromForecast(this._forecastLines, parseInt(this.selectedWeeks, 10));
                console.log('weekColumns ' + JSON.stringify(this.weekColumns));
                this.cashFlowData = this.transformDataToCashflowRows(this._projectData, this._activeCashflow, this._forecastLines, this.weekColumns);
                console.log('cash data 2 ', JSON.stringify(this.cashFlowData));
                ////debugger;
                // Version options logic (same as provided)
                this.versionOptions = [];
                const versions = pageData.allCashflows || [];
                if (versions.length) {
                    // map to a label/value array
                    this.versionOptions = versions.map(cf => ({
                        label: cf.Version_Number__c,
                        value: cf.Id
                    }));
                    // pre‐select the one we actually loaded
                    this.selectedVersion = pageData.activeCashflow?.Id || versions[versions.length - 1].Id;
                } else {
                    this.versionOptions = [{ label: 'Version 1', value: '' }];
                    this.selectedVersion = '';
                }
                // 1. Find actual row index
const idx = this.cashFlowData.findIndex(r => r.id === 'ReceiptPayApp');
if (idx === -1) {
    console.warn('❌ ReceiptPayApp row not found in cashFlowData');
    return;
}
const receiptRow = this.cashFlowData[idx];

// 2. Build week map
const weekMap = new Map();
(this.weekColumns || []).forEach(col => {
    const weekStart = new Date(col.date);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    weekMap.set(col.date, { start: weekStart, end: weekEnd });
});

// 3. Tally pay apps into totalsByDate
const totalsByDate = {};
(this.payApplicationData || []).forEach(app => {
    const value = parseFloat(app.Projected_payment__c || app.Amount_applied_to_loan__c || 0);
    const created = new Date(app.CreatedDate);
    let matched = false;

    for (const [dateKey, range] of weekMap.entries()) {
        if (created >= range.start && created <= range.end) {
            totalsByDate[dateKey] = (totalsByDate[dateKey] || 0) + value;
            matched = true;
            break;
        }
    }

    if (!matched && this.weekColumns.length > 0) {
        const lastDate = this.weekColumns[this.weekColumns.length - 1].date;
        totalsByDate[lastDate] = (totalsByDate[lastDate] || 0) + value;
        console.warn(`⚠️ No match found, assigned to: ${lastDate}`);
    }
});

// 4. Update the row directly by reference
receiptRow.calculatedTotal = 0;
receiptRow.weeks.forEach(cell => {
    const amount = totalsByDate[cell.date] || 0;
    cell.value = amount;
    cell.weeklyAmount = amount;
    receiptRow.calculatedTotal += amount;
});

// 5. Re-assign cashFlowData to trigger UI refresh
this.cashFlowData = [...this.cashFlowData];

// 6. Confirm!
console.log('✅ Final ReceiptPayApp row:', JSON.stringify(receiptRow));

                this.transformGrid();
                this.recalculateAllTotals();
                this.coverPageData = this.transformDataToCoverPage(this._projectData, this._activeCashflow);
                //this.calculatorData = this.transformDataToCalculator(this._projectData, this._activeCashflow, this._transactions);
                this.calculatorData = this.transformDataToCalculator();

                this.disbursementData = pageData.disbursements || [];
                console.log('disburesementData ' + this.disbursementData);

                this.payApplicationData = pageData.payApplications || [];

                this.transactionData = pageData.transactions || [];

                this.projectData = pageData.project || [];

                this._activeCashflow = pageData.activeCashflow || [];

                this._account = pageData.account || [];



                // projected calculator's loan disbursement & pay application

                // this.cashFlowData = calculateLoanDisbursementCashFlow(
                //     this.transactionData,         // use the raw transactions you fetched from Apex
                //     this.disbursementData,      // the array of disbursements from Apex
                //     this.cashFlowData           // the “base” cashFlowData[] you just built
                // );

                console.log('cash data 3 ', JSON.stringify(this.cashFlowData));


                // this.cashFlowData = calculatePayApplicationCashFlow(
                //     this.payApplicationData,    // payApplications from Apex
                //     this.transactionData,         // transactions again (for grouping pay-app txns)
                //     this.projectData,           // projectData from Apex (for retainage %)
                //     this.cashFlowData           // the array that already has loan-disb updates
                // );

                console.log('cash data final ', JSON.stringify(this.cashFlowData));

            } else {
                throw new Error('No data returned from Apex.');
            }
        } catch (error) {
            console.error('[CashflowContainer] Error loading cashflow data:', JSON.stringify(error));
            this.error = 'Failed to load cashflow data: ' + (error.body ? error.body.message : (error.message ? error.message : 'Unknown error'));
            this.projectName = 'Error Loading Project';
            this.showToast('Error', this.error, 'error');
            //this.loadSampleDataForFallback();
        } finally {
            this.isLoading = false;
        }
    }

    get disbursementData() {
        return this.disbursementData;
    }

    get payApplicationData() {
        return this.payApplicationData;
    }

    get transactionData() {
        return this.transactionData;
    }

    get projectData() {
        console.log('project data 2 ' + JSON.stringify(this.projectData));
        return this.projectData;
    }

    defaultRecordTypeId;
    
          @wire(getObjectInfo, { objectApiName: CASHFLOW_LINE_ITEM_OBJECT })
          handleObjectInfo({ data, error }) {
            if (data) {
              this.defaultRecordTypeId = data.defaultRecordTypeId;
            } else if (error) {
              console.error('Error loading object info', error);
            }
          }
    
          @wire(getPicklistValues, {
            recordTypeId: '$defaultRecordTypeId',
            fieldApiName: CATEGORY_FIELD
          })
          handleCategoryPicklist({ data, error }) {
            if (data) {
              // data.values is an array of { label, value, validFor, ... }
              this.expenseCategoryOptions = data.values;
            } else if (error) {
              console.error('Error loading picklist values', error);
            }
          }

    handleWeekColumnsChange(event) {
        // pull the filtered weeks from the sidebar
        this.weekColumns = event.detail.weekColumns;
        const cols = Array.isArray(this.weekColumns)
            ? this.weekColumns
            : [];

        // get the count
        const count = cols.length;
        this.selectedWeeks = count;
        this.cashFlowData = this.transformDataToCashflowRows(this._projectData, this._activeCashflow, this._forecastLines, this.weekColumns);
        // this.cashFlowData = calculateLoanDisbursementCashFlow(
        //     this._transactions,
        //     this.disbursementData,
        //     this.cashFlowData
        // );

        // 3) Overwrite “PayAppSubmitted” / “LessRetainage” rows
        // this.cashFlowData = calculatePayApplicationCashFlow(
        //     this.payApplicationData,
        //     this._transactions,
        //     this.projectData,
        //     this.cashFlowData
        // );

        this.recalculateAllTotals();
        console.log('Parent got filtered weeks:', this.weekColumns);
    }

    loadSampleDataForFallback() { /* ... (same as provided) ... */
        console.warn('[CashflowContainer] Loading sample data as fallback.');
        const sampleCfData = generateSampleCashflowData();
        this.projectName = sampleCfData.projectName;
        this.weekColumns = sampleCfData.weekColumns;
        this.cashFlowData = sampleCfData.rows;
        this.coverPageData = generateSampleCoverData();
        this.calculatorData = generateSampleCalculatorData();

        if (this.versionOptions.length === 0) {
            this.versionOptions = [{ label: 'Sample 1.0', value: 'sample_1.0' }];
            this.selectedVersion = 'sample_1.0';
        }
        this.recalculateAllTotals();
    }

    generateWeekColumnsFromForecast(forecastLines, numberOfWeeksToDisplay = 52) {
        let overallStartDate;
        const uniqueDates = new Set();

        if (forecastLines && forecastLines.length > 0) {
            forecastLines.forEach(line => {
                if (line.Week_Start_Date__c) {
                    uniqueDates.add(line.Week_Start_Date__c);
                }
            });
        }

        if (uniqueDates.size > 0) {
            const sortedDates = Array.from(uniqueDates).sort((a, b) => new Date(a + 'T00:00:00Z') - new Date(b + 'T00:00:00Z'));
            overallStartDate = new Date(sortedDates[0] + 'T00:00:00Z');
        } else if (this._activeCashflow && this._activeCashflow.Forecast_Start_Date__c) {
            overallStartDate = new Date(this._activeCashflow.Forecast_Start_Date__c + 'T00:00:00Z');
        } else {
            console.log('[CashflowContainer] No forecast dates, generating default week columns from today.');
            return this.createDefaultWeekColumns(numberOfWeeksToDisplay);
        }

        const columns = [];
        for (let i = 0; i < numberOfWeeksToDisplay; i++) {
            const currentDate = new Date(overallStartDate.valueOf());
            currentDate.setUTCDate(currentDate.getUTCDate() + (i * 7));
            const dateStr = currentDate.toISOString().split('T')[0];
            columns.push({
                id: `week${i}-${dateStr}`,
                label: `${currentDate.getUTCMonth() + 1}/${currentDate.getUTCDate()}`,
                date: dateStr,
                formattedDate: `${currentDate.getUTCMonth() + 1}/${currentDate.getUTCDate()}/${String(currentDate.getUTCFullYear()).slice(-2)}`
            });
        }
        return columns;
    }
    createDefaultWeekColumns(numberOfWeeks) { /* ... (same as provided) ... */
        let columns = [];
        let startDate = new Date();
        const localDay = startDate.getDay();
        const diff = startDate.getDate() - localDay + (localDay === 0 ? -6 : 1);
        startDate = new Date(startDate.setDate(diff));
        startDate.setUTCHours(0, 0, 0, 0);

        for (let i = 0; i < numberOfWeeks; i++) {
            const weekStartDate = new Date(startDate.valueOf());
            weekStartDate.setUTCDate(startDate.getUTCDate() + i * 7);
            const dateStr = weekStartDate.toISOString().split('T')[0];
            columns.push({
                id: `week${i}-default-${dateStr}`,
                label: `${weekStartDate.getUTCMonth() + 1}/${weekStartDate.getUTCDate()}`,
                date: dateStr,
                formattedDate: `${weekStartDate.getUTCMonth() + 1}/${weekStartDate.getUTCDate()}/${String(weekStartDate.getUTCFullYear()).slice(-2)}`
            });
        }
        return columns;
    }

    transformDataToCashflowRows(project, activeCashflow, forecastLines, weekColumns) {
        console.log('[CashflowContainer] transformDataToCashflowRows starting (parent/child model)...');
        const newRows = [];
        // forecastMapByCatAndWeek will store the parent Cashflow_Line_Item__c object,
        // which is assumed to already contain its children in a property like 'Cashflow_Line_Item_Details__r'.
        const forecastMapByCatAndWeek = new Map();

        (forecastLines || []).forEach(parentLine => { // parentLine is a Cashflow_Line_Item__c
            if (parentLine.Line_Item_Category__c && parentLine.Week_Start_Date__c) {
                const dateKey = parentLine.Week_Start_Date__c;
                const mapKey = `${parentLine.Line_Item_Category__c}_${dateKey}`;
                // Assuming one unique parent Cashflow_Line_Item__c per category and week_start_date from backend
                forecastMapByCatAndWeek.set(mapKey, parentLine);
            }
        });
        console.log('forecastMapByCatAndWeek', forecastMapByCatAndWeek);
        //debugger;

        const createEmptyWeeksForDataRow = (rowId, rowLabel, rowType) => {
            return (weekColumns || []).map((col) => {
                const hasPopover = POPOVER_ROW_TYPES.includes(rowType);
                return {
                    id: `cell-${rowId}-${col.id}`, weekIdentifier: col.id, value: 0,
                    weekLabel: col.label, rowLabel: rowLabel,
                    cellClass: 'slds-text-align_center' + (hasPopover ? ' cell-clickable' : ''),
                    hasPopover: hasPopover, date: col.date,
                    salesforceIds: [], // Will hold parent Cashflow_Line_Item__c.Id
                    originalLineItems: [], // Will hold child Cashflow_Line_Item_Detail__c records
                    category: rowType === 'DATA_EXPENSE' ? rowLabel : null, // Default category
                    expenseCategory: rowType === 'DATA_EXPENSE' ? rowLabel : null,
                    variableFixed: 'Fixed', paymentFrequency: 'Weekly', weeklyAmount: 0, paymentTerm: 'Net 30',
                    isDifference: rowType !== 'DATA_CALCULATED_SUMMARY' && rowType !== 'DATA_CALCULATED_TOTAL' && rowType !== 'DATA_READONLY'
                };
            });
        };

        const populateWeeksForRow = (weeksArray, categoryForForecast, rowType) => {
            weeksArray.forEach(weekCell => {
                const forecastKey = `${categoryForForecast}_${weekCell.date}`;
                const parentForecastEntry = forecastMapByCatAndWeek.get(forecastKey); // This is the parent Cashflow_Line_Item__c

                if (parentForecastEntry) {
                    weekCell.value = parseFloat(parentForecastEntry.Planned_Amount__c || 0); // Aggregate value from parent
                    weekCell.salesforceIds = [parentForecastEntry.Id]; // Store parent's SF ID
                    // Use the assumed relationship name for children. Ensure it's correct.
                    weekCell.originalLineItems = parentForecastEntry[CHILD_LINE_ITEMS_REL_NAME] ? JSON.parse(JSON.stringify(parentForecastEntry[CHILD_LINE_ITEMS_REL_NAME])) : [];

                    weekCell.weeklyAmount = weekCell.value; // Cell's display amount is the aggregate
                    weekCell.category = parentForecastEntry.Line_Item_Category__c;
                    weekCell.expenseCategory = parentForecastEntry.Line_Item_Category__c;

                    // Default popover fields can come from parent as a template, or first child if children exist
                    if (weekCell.originalLineItems.length > 0) {
                        const repChildItem = weekCell.originalLineItems[0]; // A representative child
                        // Assuming child items might have these specific fields, otherwise use parent's as fallback
                        weekCell.variableFixed = repChildItem.Sub_Amount_Variation__c || parentForecastEntry.Sub_Amount_Variation__c || 'Fixed';
                        weekCell.paymentFrequency = repChildItem.Sub_Payment_Frequency__c || parentForecastEntry.Sub_Payment_Frequency__c || 'Weekly';
                        weekCell.paymentTerm = repChildItem.Sub_Payment_Terms__c || parentForecastEntry.Sub_Payment_Terms__c || 'Net 30';
                    } else {
                        weekCell.variableFixed = parentForecastEntry.Sub_Amount_Variation__c || 'Fixed';
                        weekCell.paymentFrequency = parentForecastEntry.Sub_Payment_Frequency__c || 'Weekly';
                        weekCell.paymentTerm = parentForecastEntry.Sub_Payment_Terms__c || 'Net 30';
                    }
                }
                weekCell.isDifference = rowType !== 'DATA_CALCULATED_SUMMARY' && rowType !== 'DATA_CALCULATED_TOTAL' && rowType !== 'DATA_READONLY';
                if (weekCell.isDifference && POPOVER_ROW_TYPES.includes(rowType)) {
                    weekCell.cellClass = (weekCell.cellClass || '').replace('cell-clickable', '').replace('cell-difference wholelightblue', '').trim();
                    if (POPOVER_ROW_TYPES.includes(rowType)) weekCell.cellClass += ' cell-clickable';
                    weekCell.cellClass += ' cell-difference wholelightblue';
                }
            });
        };

        const createActualDataRow = (rowId, label, type, level, categoryForForecast, isDifferenceOverride) => {
            let weeks = createEmptyWeeksForDataRow(rowId, label, type);
            if (categoryForForecast) {
                populateWeeksForRow(weeks, categoryForForecast, type);
            }
            const isDiffRow = typeof isDifferenceOverride === 'boolean' ? isDifferenceOverride :
                (type !== 'DATA_CALCULATED_SUMMARY' && type !== 'DATA_CALCULATED_TOTAL' && type !== 'DATA_READONLY');

            weeks.forEach(w => {
                w.isDifference = isDiffRow;
                let currentCellClass = w.cellClass || 'slds-text-align_center';
                currentCellClass = currentCellClass.replace('cell-clickable', '').replace('cell-difference wholelightblue', '').trim();
                if (POPOVER_ROW_TYPES.includes(type)) currentCellClass += ' cell-clickable';
                if (isDiffRow && POPOVER_ROW_TYPES.includes(type)) currentCellClass += ' cell-difference wholelightblue';
                w.cellClass = currentCellClass;
            });
            return {
                id: rowId, label: label, isSectionHeader: false, isEditable: POPOVER_ROW_TYPES.includes(type),
                type: type, level: level, parentId: null, weeks: weeks,
                calculatedTotal: weeks.reduce((sum, cell) => sum + (cell.value || 0), 0),
                rowClass: `data-row ${CALCULATED_ROW_TYPES.includes(type) ? 'calculated-row' : ''} ${type === 'DATA_CALCULATED_SUMMARY' ? 'summary-row' : ''}`,
                indentStyle: `padding-left: ${level * 1.5}rem;`
            };
        };

        // --- Define categories & Row Creation (same structure as provided, but data source within cells changes) ---

        const revenueLineItemCategories = new Set();
        const expenseCategoriesForRows = new Set();

        const financingLineItemCategoriesFromConfig = new Set([
            activeCashflow?.MFLoanDisbursementCategory__c, activeCashflow?.MFLoanRepaymentCategory__c,
            activeCashflow?.OtherSSVCategory__c, activeCashflow?.AlternativeLoanCategory__c
        ].filter(Boolean));

        (forecastLines || []).forEach(line => {
            if (line.Line_Item_Category__c && !financingLineItemCategoriesFromConfig.has(line.Line_Item_Category__c)) {
                if (line.Type__c === 'Project Revenue') {
                    revenueLineItemCategories.add(line.Line_Item_Category__c);
                } else if (line.Type__c === 'Project Cost') {
                    expenseCategoriesForRows.add(line.Line_Item_Category__c);
                }
            }
        });


        newRows.push(createSectionHeader('PROJECT REVENUE'));
        //debugger;
        if (revenueLineItemCategories.size > 0) {
            Array.from(revenueLineItemCategories).sort().forEach(category => {
                console.log('category', category);
                const rowId = `revenue-${category.replace(/[^a-zA-Z0-9]/g, '') || Math.random().toString(36).substr(2, 5)}`;
                console.log('rowId', rowId);
                newRows.push(createActualDataRow(rowId, category, 'DATA_REVENUE_EDITABLE', 1, category, true));
            });

        } else {
            // If no specific revenue items are found in the forecast, add default placeholders
            newRows.push(createActualDataRow('payApp', 'Pay App to be Submitted', 'DATA_REVENUE_EDITABLE', 1, 'Invoice Submission', true));
            newRows.push(createActualDataRow('retainage', 'Less Retainage', 'DATA_REVENUE_EDITABLE', 1, 'Retainage', true));

        }
        newRows.push(createActualDataRow('ProjectedNetPayApp', 'Projected Net Pay App', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createSectionHeader('PROJECT COSTS', true));
        //debugger;
        console.log('forecast lines', forecastLines);
        console.log('revenueLineItemCategories', revenueLineItemCategories);
        console.log('financingLineItemCategoriesFromConfig', financingLineItemCategoriesFromConfig);

        const distinctForecastCategories = new Set((forecastLines || []).map(line => line.Line_Item_Category__c).filter(Boolean));

        distinctForecastCategories.forEach(cat => {
            if (!revenueLineItemCategories.has(cat) && !financingLineItemCategoriesFromConfig.has(cat)) {
                expenseCategoriesForRows.add(cat);
            }
        });
        // expenseCategoriesForRows.clear(); // Clear any old categories

        // (this.expenseCategoryOptions || []).forEach(option => {
        //     const category = option.value;
        //     // Skip if already used for financing or revenue
        //     if (!revenueLineItemCategories.has(category) && !financingLineItemCategoriesFromConfig.has(category)) {
        //         expenseCategoriesForRows.add(category);
        //     }
        // });
        console.log('[CashflowContainer] Identified Expense Categories for dynamic rows:', Array.from(expenseCategoriesForRows));

        
        if (expenseCategoriesForRows.size > 0) {
            Array.from(expenseCategoriesForRows).sort().forEach(category => {
                const rowId = `expense-${category.replace(/[^a-zA-Z0-9]/g, '') || Math.random().toString(36).substr(2, 5)}`;
                newRows.push(createActualDataRow(rowId, category, 'DATA_EXPENSE', 1, category, true));
            });

        } else {
            console.log('[CashflowContainer] No dynamic expense categories found from forecast. Adding placeholder.');
            newRows.push(createActualDataRow('ExpenseCat1Default', 'Expense Category 1 (Example)', 'DATA_EXPENSE', 1, null, true));
        }
        newRows.push(createActualDataRow('TotalProjectCost', 'Total Project Cost', 'DATA_CALCULATED_TOTAL', 1, null, false));

        newRows.push(createSectionHeader('FINANCING SOURCES'));
        if (expenseCategoriesForRows.size > 0) {
            Array.from(expenseCategoriesForRows).sort().forEach((category, idx) => {
                // idx is 0,1,2… so add 1
                const rowId = `expense-${idx + 1}`;
                newRows.push(
                    createActualDataRow(
                        rowId,
                        category,
                        'DATA_EXPENSE',
                        1,
                        category,
                        true
                    )
                );
            });
        } else {
            console.log('[CashflowContainer] No dynamic expense categories found from forecast. Adding placeholder.');
            newRows.push(createActualDataRow('ExpenseCat1Default', 'Expense Category 1 (Example)', 'DATA_EXPENSE', 1, null, true));
        }
        newRows.push(createActualDataRow('TotalProjectCost1', 'Total Project Cost', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('MFLoanDisb', 'MF Loan Disbursement', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFLoanDisbursementCategory__c || 'MF Loan Disbursement', true));
        newRows.push(createActualDataRow('ReceiptPayApp', 'Receipt of Pay App', 'DATA_READONLY', 1, null, true));
        newRows.push(createActualDataRow('TotalSources', 'Total Sources of Cash', 'DATA_CALCULATED_TOTAL', 1, null, false));

        newRows.push(createSectionHeader('USES'));
        newRows.push(createActualDataRow('MFLoanRepay', 'MF Loan Repayment', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFLoanRepaymentCategory__c || 'MF Loan Repayment', true));
        newRows.push(createActualDataRow('ProjectCostsPaid', 'Project Costs Paid That Week', 'DATA_FINANCING_EDITABLE', 1, null, true));
        newRows.push(createActualDataRow('otherSSV', 'OTHER SSV PAYMENTS REQ\'d', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.OtherSSVCategory__c || 'OTHER SSV PAYMENTS REQ\'d', true));
        newRows.push(createActualDataRow('alternativeLoanPayment', 'Alternative Loan Payment', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.AlternativeLoanCategory__c || 'Alternative Loan Payment', true));
        newRows.push(createActualDataRow('TotalUses', 'Total Uses of Cash', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('payAppAppliedmfloan', '% of Pay APP Applied to MF Loan', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.AlternativeLoanCategory__c || 'Alternative Loan Payment', true));
        newRows.push(createSectionHeader('CASH FLOW SUMMARY'));
        newRows.push(createActualDataRow('NetWeeklyCashFlow', 'Net Weekly Cash Flow', 'DATA_CALCULATED_SUMMARY', 1, null, false));
        newRows.push(createActualDataRow('AccumulatedSurplus', 'Accumulated Surplus/(Deficit)', 'DATA_CALCULATED_SUMMARY', 1, null, false));
        newRows.push(createSectionHeader('LOAN SCHEDULE'));
        newRows.push(createActualDataRow('NetWeeklyCashflowSurplus', 'Net Weekly Cashflow- SURPLUS/(DEFICIT)', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('AutomatedWeeklyCashflowSurplus', 'Accumulated Weekly Cashflow- SURPLUS/(DEFICIT)', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('BeginningLoanBalance', 'Beginning Loan Balance', 'DATA_CALCULATED_TOTAL', 1, activeCashflow?.MFOriginationFeeCategory__c ?? null, false));
        newRows.push(createActualDataRow('LoanDisb', 'Loan Disbursements', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFLoanDisbursementCategory__c, true));
        newRows.push(createActualDataRow('MFOriginationFee', 'MF Origination Fee', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFOriginationFeeCategory__c ?? null, true));
        newRows.push(createActualDataRow('MFAccruedInterest', 'MF Accrued Interest', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFAccruedInterestCategory__c ?? null, true));
        newRows.push(createActualDataRow('PaymentAppliedToMFLoan', 'Payment Applied to MF Loan', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFLoanRepaymentCategory__c, true));
        newRows.push(createActualDataRow('EndingLoanBalance', 'Ending Loan Balance', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('LoanToValue', 'Loan to Value', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('ContractPoInvoiceValue', 'Contract/P.O./Invoice value', 'DATA_CALCULATED_TOTAL', 1, null, false));
        // newRows.push(createActualDataRow('LoanToValue', 'Loan to Value', DATA_CALCULATED_TOTAL,1,null, false));
        // newRows.push(createActualDataRow('ContractPoInvoiceValue', 'Contract/P.O./Invoice value', DATA_CALCULATED_TOTAL,1, null, false));
        const getRow = id => newRows.find(r => r.id === id);
        const nwcs = getRow('NetWeeklyCashflowSurplus');
        const awcs = getRow('AutomatedWeeklyCashflowSurplus');
        const bR = getRow('BeginningLoanBalance');
        const dR = getRow('MFLoanDisb');
        const fR = getRow('MFOriginationFee');
        const iR = getRow('MFAccruedInterest');
        const pR = getRow('PaymentAppliedToMFLoan');
        const eR = getRow('EndingLoanBalance');

        bR.weeks.forEach((cell, idx) => { cell.value = idx === 0 ? 0 : eR.weeks[idx - 1].value; cell.weeklyAmount = cell.value; });
        bR.calculatedTotal = bR.weeks.reduce((sum, c) => sum + c.value, 0);
        eR.weeks.forEach((cell, idx) => {
            const begin = bR.weeks[idx].value;
            const disb = dR.weeks[idx].value;
            const fee = fR.weeks[idx].value;
            const int = iR.weeks[idx].value;
            const pay = pR.weeks[idx].value;
            cell.value = begin + disb + fee + int + pay;
            cell.weeklyAmount = cell.value;
        });
        eR.calculatedTotal = eR.weeks.reduce((sum, c) => sum + c.value, 0);
        console.log('[CashflowContainer] transformDataToCashflowRows finished.');
        return newRows;

        console.log('newRows ' + JSON.stringify(newRows));
    }

    transformDataToCoverPage(projectData, activeCashflow) {
        if (!projectData && !activeCashflow) return generateSampleCoverData();
        const pData = projectData || {};
        const cfData = activeCashflow || {};
        let clientName = pData.Account_Name__r?.Name || pData.Account_Name__c || 'N/A';

        return {
            projectOverview: { name: pData.Name || 'N/A', client: clientName },
            keyMetrics: [
                { label: 'Requested Loan Amount', value: cfData.MF_Loan_Amount__c || pData.MF_Loan_Amount__c || 0, isCurrency: true },
                { label: 'Net Remaining Contract Value', value: cfData.Net_Remaining_Contract_Value_Snapshot__c || 0, isCurrency: true },
                { label: 'Gross LTV', value: cfData.Gross_LTV__c || pData.LTV__c || 0, isPercent: true },
                { label: 'Projected Weeks Outstanding', value: cfData.Projected_Weeks_Outstanding__c || 0 },
                { label: 'Loan #', value: pData.Project_Number__c || 'N/A' },
                { label: 'Projected MF Profitability', value: cfData.Projected_MF_Profitability__c || 0, isCurrency: true }
            ],
            recommendation: cfData.Recommendation_Snapshot__c || 'N/A',
            request: cfData.Request_Details__c || pData.Request_Details__c || 'N/A',
            usesOfProceeds: cfData.Uses_Of_Proceeds__c || pData.Uses_Of_Proceeds__c || 'N/A',
            business: pData.Project_Specific__c || 'N/A',
            repayment: cfData.Repayment_Terms__c || pData.Repayment_Terms__c || 'N/A',
            projectStatus: cfData.Status__c || 'N/A',
            dealSummary: cfData.Deal_Summary_NotesSnapshot__c || 'N/A',
            openItems: cfData.Open_Items_Snapshot__c || 'N/A',
            approvers: [/* Placeholder for future dynamic data */],
            underwriterInstructions: cfData.Underwriter_Instructions__c || 'N/A'
        };
    }
    transformDataToCalculator() { /* ... (same as provided) ... */
        return generateSampleCalculatorData();
    }

    recalculateAllTotals() { /* ... (same logic, as it works off cell.value which is now aggregate) ... */
        if (!this.cashFlowData || this.cashFlowData.length === 0 || !this.weekColumns || this.weekColumns.length === 0) {
            console.warn('[CashflowContainer] Recalculate: cashFlowData or weekColumns are empty.');
            return;
        }
        let updatedData = JSON.parse(JSON.stringify(this.cashFlowData));
        const rowMap = updatedData.reduce((map, row) => { map[row.id] = row; return map; }, {});
        const safeNum = (val) => parseFloat(val) || 0;
        const safeFixed = (val, digits = 2) => safeNum(val).toFixed(digits);

        const sec1HeaderIdx = updatedData.findIndex(r =>
            r.isSectionHeader && r.label === 'PROJECT COSTS'
        );
        const sec1SummaryIdx = updatedData.findIndex(r => r.id === 'TotalProjectCost');
        const section1ExpenseIds = updatedData
            .slice(sec1HeaderIdx + 1, sec1SummaryIdx)
            .filter(r => r.type === 'DATA_EXPENSE')
            .map(r => r.id);

        // 1b) FINANCING SOURCES block
        const sec2HeaderIdx = updatedData.findIndex(r =>
            r.isSectionHeader && r.label === 'FINANCING SOURCES'
        );
        const sec2SummaryIdx = updatedData.findIndex(r => r.id === 'TotalProjectCost1');
        const section2ExpenseIds = updatedData
            .slice(sec2HeaderIdx + 1, sec2SummaryIdx)
            .filter(r => r.type === 'DATA_EXPENSE')
            .map(r => r.id);

        this.weekColumns.forEach((col, weekIdx) => {
            const getWeekVal = (rowId) => safeNum(rowMap[rowId]?.weeks[weekIdx]?.value);

            // if (rowMap.ProjectedNetPayApp) rowMap.ProjectedNetPayApp.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('payApp') + getWeekVal('retainage')));
            
            const getWeekValByLabel = (label) => {
                const row = updatedData.find(r => r.label === label);
                return row?.weeks?.[weekIdx]?.value || 0;
            };
            console.log('Pay App to be Submitted' + getWeekValByLabel('Pay App to be Submitted'));
            console.log('Less retainage' + getWeekValByLabel('Less Retainage'));
            if (rowMap.ProjectedNetPayApp) {
                rowMap.ProjectedNetPayApp.weeks[weekIdx].value = parseFloat(
                    safeFixed(getWeekValByLabel('Pay Application to be Submitted') + getWeekValByLabel('Less Retainage'))
                );
            }
            const expenseSum = updatedData.filter(r => r.type === 'DATA_EXPENSE').reduce((sum, r) => sum + safeNum(r.weeks[weekIdx]?.value), 0);
            // if (rowMap.TotalProjectCost) rowMap.TotalProjectCost.weeks[weekIdx].value = parseFloat(safeFixed(expenseSum));
            if (rowMap.TotalProjectCost) {
                const sum1 = section1ExpenseIds.reduce((sum, id) => sum + getWeekVal(id), 0);
                const cell1 = rowMap.TotalProjectCost.weeks[weekIdx];
                cell1.value = parseFloat(safeFixed(sum1));
                cell1.weeklyAmount = cell1.value;
            }
            if (rowMap.ProjectCostsPaid && !rowMap.ProjectCostsPaid.weeks[weekIdx].isManuallySet) {
                rowMap.ProjectCostsPaid.weeks[weekIdx].value = parseFloat(safeFixed(-expenseSum));
            }
            // if (rowMap.ReceiptPayApp) rowMap.ReceiptPayApp.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('ProjectedNetPayApp')));
            if (rowMap.LoanDisb) rowMap.LoanDisb.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('MFLoanDisb')));
            // if (rowMap.TotalSources) rowMap.TotalSources.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('MFLoanDisb') + getWeekVal('ReceiptPayApp')));
            if (rowMap.TotalSources) rowMap.TotalSources.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('ReceiptPayApp')));
            let projectCostsPaidMagnitude = Math.abs(getWeekVal('ProjectCostsPaid'));
            let totalPositiveUses = getWeekVal('MFLoanRepay') + projectCostsPaidMagnitude + getWeekVal('otherSSV') + getWeekVal('alternativeLoanPayment');
            if (rowMap.TotalUses) rowMap.TotalUses.weeks[weekIdx].value = parseFloat(safeFixed(-totalPositiveUses));
            if (rowMap.NetWeeklyCashFlow) rowMap.NetWeeklyCashFlow.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('TotalSources') + getWeekVal('TotalUses')));
            if (rowMap.TotalProjectCost1) {
                const sum2 = section2ExpenseIds.reduce((sum, id) => sum + getWeekVal(id), 0);
                const cell2 = rowMap.TotalProjectCost1.weeks[weekIdx];
                cell2.value = parseFloat(safeFixed(sum2));
                cell2.weeklyAmount = cell2.value;
            }
            if (rowMap.NetWeeklyCashflowSurplus) {
                const val = getWeekVal('TotalSources') + getWeekVal('TotalUses');
                const weekly = parseFloat(safeFixed(val));
                const cell = rowMap.NetWeeklyCashflowSurplus.weeks[weekIdx];
                cell.value = weekly;
                cell.weeklyAmount = weekly;
            }
            if (rowMap.BeginningLoanBalance) { rowMap.BeginningLoanBalance.weeks[weekIdx].value = weekIdx === 0 ? 0 : safeNum(rowMap.EndingLoanBalance.weeks[weekIdx - 1].value); }
            if (rowMap.BeginningLoanBalance) {
                const cell = rowMap.BeginningLoanBalance.weeks[weekIdx];
                if (!cell.isManuallySet) {
                    const startingBalance = safeNum(this._activeCashflow?.Beginning_Loan_Balance__c);
                    const val = weekIdx === 0
                        ? startingBalance
                        : safeNum(rowMap.EndingLoanBalance.weeks[weekIdx - 1].value);
                    cell.value = parseFloat(safeFixed(val));
                    cell.weeklyAmount = parseFloat(safeFixed(val));
                }
            }
            // 2) Ending Loan Balance = begin + disbursements + fee + interest – payment
            if (rowMap.EndingLoanBalance) {
                const begin = safeNum(rowMap.BeginningLoanBalance.weeks[weekIdx].value);
                const disb = getWeekVal('MFLoanDisb');
                const fee = getWeekVal('MFOriginationFee');
                const interest = getWeekVal('MFAccruedInterest');
                const payment = getWeekVal('PaymentAppliedToMFLoan');
                const endVal = begin + disb + fee + interest + payment;
                rowMap.EndingLoanBalance.weeks[weekIdx].value = parseFloat(safeFixed(endVal));
            }

            if (rowMap.ContractPoInvoiceValue) {
                const cell = rowMap.ContractPoInvoiceValue.weeks[weekIdx];
                const contractVal = safeNum(this._activeCashflow?.Contract_PO_Invoice_Value__c);
                cell.value = parseFloat(safeFixed(contractVal));
                cell.weeklyAmount = cell.value;
            }

            // 2) Loan to Value = EndingLoanBalance / ContractPoInvoiceValue  (or 0 if contractVal is 0)
            if (rowMap.LoanToValue) {
                const cell = rowMap.LoanToValue.weeks[weekIdx];
                const ending = safeNum(getWeekVal('EndingLoanBalance'));
                const contract = safeNum(getWeekVal('ContractPoInvoiceValue'));
                console.log('ending / contract' + ending / contract);
                const ltv = contract ? ending / contract : 0;
                cell.value = parseFloat(safeFixed(ltv));
                cell.weeklyAmount = cell.value;
            }
        });

        if (rowMap.AutomatedWeeklyCashflowSurplus && rowMap.NetWeeklyCashflowSurplus) {
            let running = 0;
            this.weekColumns.forEach((col, weekIdx) => {
                const netVal = safeNum(rowMap.NetWeeklyCashflowSurplus.weeks[weekIdx].value);
                running += netVal;
                const accCell = rowMap.AutomatedWeeklyCashflowSurplus.weeks[weekIdx];
                accCell.value = parseFloat(safeFixed(running));
                accCell.weeklyAmount = accCell.value;
            });
        }

        let accumulatedSurplusVal = 0;
        this.weekColumns.forEach((col, weekIdx) => {
            const netWeeklyFlow = safeNum(rowMap.NetWeeklyCashFlow?.weeks[weekIdx]?.value);
            accumulatedSurplusVal += netWeeklyFlow;
            if (rowMap.AccumulatedSurplus) rowMap.AccumulatedSurplus.weeks[weekIdx].value = parseFloat(safeFixed(accumulatedSurplusVal));
        });

        updatedData.forEach(row => {
            if (!row.isSectionHeader) {
                row.calculatedTotal = parseFloat(safeFixed(row.weeks.reduce((sum, cell) => sum + safeNum(cell.value), 0)));
                if (row.id === 'AccumulatedSurplus' && row.weeks.length > 0) {
                    row.calculatedTotal = parseFloat(safeFixed(row.weeks[row.weeks.length - 1].value));
                }
                row.weeks.forEach(cell => {
                    let cellClasses = cell.computedClass || cell.cellClass || 'slds-text-align_center';
                    cellClasses = cellClasses.replace(' cell-positive-background', '').replace(' cell-negative-background', '').trim();
                    if (row.type === 'DATA_CALCULATED_SUMMARY') {
                        if (cell.value >= 0) cellClasses += ' cell-positive-background';
                        else if (cell.value < 0) cellClasses += ' cell-negative-background';
                    }
                    cell.computedClass = cellClasses.trim();
                });
            }
        });
        this.cashFlowData = updatedData;
    }

    handleFromCalculatorTab(event) {
        // event.detail is a new cashFlowData[] from some child
        let updated = event.detail;

        // Apply loan-disbursement logic:
        // updated = calculateLoanDisbursementCashFlow(
        //     this.transactionData,
        //     this.disbursementData,
        //     updated
        // );

        // Apply pay-app logic:
        // updated = calculatePayApplicationCashFlow(
        //     this.payApplicationData,
        //     this.transactionData,
        //     this.projectData,
        //     updated
        // );

        // Recalc totals, then re-emit
        this.cashFlowData = updated;
        this.recalculateAllTotals();
        this.dispatchEvent(
            new CustomEvent('cashflowdataupdate', { detail: this.cashFlowData, bubbles: true, composed: true })
        );
    }

    handlePopoverSave(event) {
        // updatedCellData now refers to a child line item from the popover
        // originalCellData refers to the parent cell in the grid
        const { originalCellData, updatedCellData } = event.detail;

        if (!originalCellData || !originalCellData.rowId || !originalCellData.weekIdentifier || !originalCellData.date) {
            this.showToast('Error', 'Invalid data from modal. Missing key identifiers for original cell.', 'error');
            console.error('[CashflowContainer] Invalid popoversave event detail (originalCellData):', JSON.stringify(originalCellData));
            return;
        }

        let cashFlowDataCopy = JSON.parse(JSON.stringify(this.cashFlowData));
        const targetRow = cashFlowDataCopy.find(row => row.id === originalCellData.rowId);

        if (targetRow) {
            const targetCell = targetRow.weeks.find(cell =>
                cell.weekIdentifier === originalCellData.weekIdentifier && cell.date === originalCellData.date
            );

            if (targetCell) {
                let childFoundAndUpdated = false;


                // Recalculate the cell's aggregate value (cell.value) from its children
                targetCell.value = updatedCellData.value;
                targetCell.weeklyAmount = targetCell.value; // Update cell's display amount
                targetCell.isManuallySet = true;
                this.cashFlowData = cashFlowDataCopy;
                this.recalculateAllTotals(); // Recalculate all summary rows, etc.

                // Highlight cell if aggregate value changed
                const originalAggregateAmount = parseFloat(originalCellData.payload?.weeklyAmount || originalCellData.value) || 0;
                if (targetCell.value !== originalAggregateAmount) {
                    this.cashFlowData = this.cashFlowData.map(r => {
                        if (r.id !== originalCellData.rowId) return r;
                        return {
                            ...r,
                            weeks: r.weeks.map(c => {
                                if (c.weekIdentifier === originalCellData.weekIdentifier && c.date === originalCellData.date) {
                                    const existing = c.computedClass || c.cellClass || '';
                                    return { ...c, computedClass: `${existing} cell-updated`.trim() };
                                }
                                return c;
                            })
                        };
                    });
                }
                this.showToast('Success', 'Details updated locally.', 'success');
            } else {
                this.showToast('Error', 'Could not find specific cell to update.', 'error');
            }
        } else {
            this.showToast('Error', 'Could not find row to update.', 'error');
        }
    }

    handlePopoverUnlink(event) {
        // Expects originalCellData to identify the parent cell, and originalCellData.childIdToUnlink for the specific child
        const { rowId, weekIdentifier, date, childIdToUnlink } = event.detail.originalCellData;
        console.log('[CashflowContainer] Popover Unlink event received for child ID:', childIdToUnlink, 'in cell:', rowId, weekIdentifier, date);

        if (!childIdToUnlink) {
            this.showToast('Warning', 'No specific child item ID provided for unlinking.', 'warning');
            return;
        }

        let cashFlowDataCopy = JSON.parse(JSON.stringify(this.cashFlowData));
        const targetRow = cashFlowDataCopy.find(r => r.id === rowId);

        if (targetRow) {
            const targetCell = targetRow.weeks.find(c => c.weekIdentifier === weekIdentifier && c.date === date);
            if (targetCell && targetCell.originalLineItems) {
                targetCell.originalLineItems = targetCell.originalLineItems.filter(child => child.id !== childIdToUnlink);

                // Recalculate the aggregate value for the cell
                targetCell.value = targetCell.originalLineItems.reduce((sum, child) => {
                    return sum + (parseFloat(child.Amount__c) || 0); // Ensure Amount__c is correct
                }, 0);
                targetCell.weeklyAmount = targetCell.value;

                if (targetCell.originalLineItems.length === 0) {
                    // Optionally reset other popover-related fields if all children are gone
                    targetCell.category = targetRow.label;
                    // targetCell.variableFixed = 'Fixed'; // etc.
                }

                this.cashFlowData = cashFlowDataCopy;
                this.recalculateAllTotals();
                this.showToast('Info', 'Item detail unlinked locally. Save to persist.', 'info');
            }
        }
    }

    handleTabSelect(event) { this.activeTab = event.target.value; }
    handleAppHeaderTabClick(event) { this.activeAppHeaderTab = event.currentTarget.dataset.tabvalue; }
    handleVersionChange(event) {
        const newCashflowId = event.detail.value;
        if (newCashflowId && newCashflowId !== this.selectedVersion) {
            this.selectedVersion = newCashflowId;
            this[NavigationMixin.Navigate]({
                type: 'standard__navItemPage',
                attributes: {
                    apiName: 'CashFlow'
                },
                state: {
                    c__recordId: newCashflowId,
                    c__projectId: this._currentProjectId
                }
            });
            this._currentCashflowId = newCashflowId;
            this.loadCashflowDetails(this._currentProjectId, this._currentCashflowId);
            this.showToast('Info', `Loading version...`, 'info');

        }
    }
    handleWeeksChange(event) { /* ... (same as provided) ... */
        this.selectedWeeks = event.detail.value;
        if (this._forecastLines) { // _forecastLines are parent CLIs
            this.weekColumns = this.generateWeekColumnsFromForecast(this._forecastLines, parseInt(this.selectedWeeks, 10));
            this.cashFlowData = this.transformDataToCashflowRows(this._projectData, this._activeCashflow, this._forecastLines, this.weekColumns);

            // 2) Overwrite “MFLoanDisb” row
            // this.cashFlowData = calculateLoanDisbursementCashFlow(
            //     this._transactions,
            //     this.disbursementData,
            //     this.cashFlowData
            // );

            // 3) Overwrite “PayAppSubmitted” / “LessRetainage” rows
            // this.cashFlowData = calculatePayApplicationCashFlow(
            //     this.payApplicationData,
            //     this._transactions,
            //     this.projectData,
            //     this.cashFlowData
            // );

            this.recalculateAllTotals();
        }
    }
    handleHeaderChange(event) { /* ... (same as provided) ... */
        const { name, value } = event.target;
        if (name === 'view') {
            this.selectedView = value;
        }
        console.log(`[CashflowContainer] Container Header Changed - ${name}: ${value}`);
    }
    handleChildToastError(event) { /* ... (same as provided) ... */
        const { title, message, variant } = event.detail;
        this.showToast(title, message, variant || 'error');
    }
    handleCalculatorEdit(event) { console.log('[CashflowContainer] Calculator Edit event:', JSON.stringify(event.detail)); }

    handleGlobalSave() {
        try {
            //debugger;
            this.isLoading = true;
            console.log('[CashflowContainer] Global Save Initiated (Parent/Child Model).');

            const parentLineItemsToUpsert = [];
            const parentLineItemIdsToDelete = new Set(); // IDs of parent Cashflow_Line_Item__c to delete

            const allChildDetailsToUpsert = [];
            const allChildDetailIdsToDelete = new Set(); // IDs of child Cashflow_Line_Item_Detail__c to delete

            this.cashFlowData.forEach(row => {
                if (POPOVER_ROW_TYPES.includes(row.type) && row.weeks) {
                    row.weeks.forEach(cell => {
                        const parentCliId = (cell.salesforceIds && cell.salesforceIds.length > 0) ? cell.salesforceIds[0] : null;
                        let originalParentData = null;
                        if (parentCliId) {
                            originalParentData = this._initialForecastLinesWithChildren.find(fl => fl.Id === parentCliId);
                        }

                        // --- Parent Cashflow_Line_Item__c DML ---
                        const currentParentPlannedAmount = parseFloat(cell.value || 0);
                        const originalParentPlannedAmount = originalParentData ? parseFloat(originalParentData.Planned_Amount__c || 0) : 0;

                        if (parentCliId) { // Existing Parent CLI
                            if (currentParentPlannedAmount !== originalParentPlannedAmount) {
                                parentLineItemsToUpsert.push({
                                    Id: parentCliId,
                                    Planned_Amount__c: currentParentPlannedAmount
                                    // Include other parent fields if they are directly editable and changed
                                });
                            }
                            // Deletion of parent: If aggregate is 0 AND no children AND it was not already 0 with no children.
                            // This rule might need refinement.
                            if (currentParentPlannedAmount === 0 &&
                                (!cell.originalLineItems || cell.originalLineItems.length === 0) &&
                                (originalParentPlannedAmount !== 0 || (originalParentData && originalParentData[CHILD_LINE_ITEMS_REL_NAME] && originalParentData[CHILD_LINE_ITEMS_REL_NAME].length > 0))) {
                                // Decide if this means delete parent or just update amount to 0.
                                // For now, we are just updating amount. Explicit delete of parent CLI handled by ondeleterow for the whole row.
                                // If unlinking all children and zeroing out value should delete parent, add: parentLineItemIdsToDelete.add(parentCliId);
                            }
                        } else if (currentParentPlannedAmount !== 0 || (cell.originalLineItems && cell.originalLineItems.length > 0)) { // New Parent CLI
                            // This row/cell represents a new parent Cashflow_Line_Item__c
                            parentLineItemsToUpsert.push({
                                // sobjectType: 'Cashflow_Line_Item__c', // Let Apex know
                                Cashflow__c: this._currentCashflowId,
                                Line_Item_Category__c: cell.expenseCategory || row.label, // Ensure cell has these
                                Week_Start_Date__c: cell.date,
                                Planned_Amount__c: currentParentPlannedAmount,
                                // Other required fields for a new parent Cashflow_Line_Item__c
                            });

                            // Children for this new parent will be in cell.originalLineItems.
                            // Apex needs to handle linking them after this new parent is inserted.
                            // Client-side temporary ID for parentCliId might be needed for children in allChildDetailsToUpsert.
                            // For now, allChildDetailsToUpsert links via existing parentCliId. This is a gap for new parents with new children in one go.
                        }

                        // --- Child Cashflow_Line_Item_Detail__c DML ---
                        if (parentCliId) { // Process children only for existing parents for simplicity in this pass
                            // Children of NEW parents need special handling (e.g. two-step save or complex Apex).
                            const originalChildrenMap = new Map();
                            if (originalParentData && originalParentData[CHILD_LINE_ITEMS_REL_NAME]) {
                                originalParentData[CHILD_LINE_ITEMS_REL_NAME].forEach(child => originalChildrenMap.set(child.Id, child));
                            }

                            const currentChildrenIdsInCell = new Set();
                            (cell.originalLineItems || []).forEach(childDetail => {
                                const isNewChild = !childDetail.id || childDetail.id.startsWith('temp_');
                                const childAmount = parseFloat(childDetail.Amount__c || childDetail.weeklyAmount || childDetail.value) || 0;

                                // Basic change detection for existing children
                                let childHasChanged = isNewChild;
                                if (!isNewChild) {
                                    const originalChild = originalChildrenMap.get(childDetail.id);
                                    if (!originalChild ||
                                        (parseFloat(originalChild.Amount__c || 0) !== childAmount) ||
                                        (originalChild.Description__c !== childDetail.Description__c)
                                    ) {
                                        childHasChanged = true;
                                    }
                                }

                                if (childHasChanged) {
                                    allChildDetailsToUpsert.push({
                                        Id: isNewChild ? null : childDetail.id,
                                        Cashflow_Line_Item__c: parentCliId,
                                        Amount__c: childAmount,
                                        Description__c: childDetail.Description__c,
                                    });
                                }
                                if (!isNewChild) {
                                    currentChildrenIdsInCell.add(childDetail.id);
                                }
                            });

                            originalChildrenMap.forEach((originalChild, originalChildId) => {
                                if (!currentChildrenIdsInCell.has(originalChildId)) {
                                    allChildDetailIdsToDelete.add(originalChildId);
                                }
                            });
                        }
                    });
                }
            });

            console.log('Parent CLIs To Upsert:', JSON.stringify(parentLineItemsToUpsert));
            console.log('Parent CLI IDs To Delete:', JSON.stringify(Array.from(parentLineItemIdsToDelete)));
            console.log('Child Details To Upsert:', JSON.stringify(allChildDetailsToUpsert));
            console.log('Child Detail IDs To Delete:', JSON.stringify(Array.from(allChildDetailIdsToDelete)));

            if (parentLineItemsToUpsert.length === 0 && parentLineItemIdsToDelete.size === 0 &&
                allChildDetailsToUpsert.length === 0 && allChildDetailIdsToDelete.size === 0) {
                this.showToast('Info', 'No changes to save.', 'info');
                this.isLoading = false;
                return;
            }


            if (this.saveAs) {
                saveAsCashflow({
                    originalCashflowId: this._currentCashflowId,
                    parentItemsToUpsert: parentLineItemsToUpsert,
                    parentItemIdsToDelete: Array.from(parentLineItemIdsToDelete)

                })
                    .then(result => {
                        this.showToast('Success', 'Cashflow data saved and new version created successfully.', 'success');
                        console.log('cashflowId ' + result.Id);
                        this[NavigationMixin.Navigate]({
                            type: 'standard__navItemPage',
                            attributes: {
                                apiName: 'CashFlow'
                            },
                            state: {
                                c__recordId: result.Id,
                                c__projectId: this._currentProjectId
                            }
                        });
                        this._currentCashflowId = result.Id;
                        this.loadCashflowDetails(this._currentProjectId, this._currentCashflowId);
                    })
                    .catch(error => {
                        console.error('[CashflowContainer] Save Error:', error);
                        this.showToast('Error', 'Failed to save cashflow data: ' + (error.body?.message || error.message || 'Unknown error'), 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
            else {
                saveCashflowDetails({
                    parentItemsToUpsert: parentLineItemsToUpsert,
                    parentItemIdsToDelete: Array.from(parentLineItemIdsToDelete),
                    childItemsToUpsert: allChildDetailsToUpsert,
                    childItemIdsToDelete: Array.from(allChildDetailIdsToDelete)
                })
                    .then(result => {
                        this.showToast('Success', 'Cashflow data saved successfully.', 'success');
                        console.log('resulted data -> ' + JSON.stringify(result));
                        this.loadCashflowDetails(this._currentProjectId, this._currentCashflowId);
                    })
                    .catch(error => {
                        console.error('[CashflowContainer] Save Error:', error);
                        this.showToast('Error', 'Failed to save cashflow data: ' + (error.body?.message || error.message || 'Unknown error'), 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
        } catch (e) {
            // This will give you the actual error message & stack
            console.error('🔥 Error inside handleGlobalSave:', e);
            this.showToast('Error', e.message || 'Unknown error', 'error');
        }
    }

    handleAddNewRow(event) { /* ... (same as provided - this adds a UI row. GlobalSave would handle backend creation of parent CLI) ... */
        const { sectionId, category } = event.detail;
        let data = JSON.parse(JSON.stringify(this.cashFlowData));
        let insertAtIndex = -1;
        let referenceLevel = 1;
        const headerIdx = data.findIndex(r => r.id === sectionId && r.isSectionHeader);

        if (headerIdx >= 0) {
            for (let i = headerIdx + 1; i < data.length; i++) {
                if (data[i].type === 'DATA_CALCULATED_TOTAL') {
                    referenceLevel = data[i - 1]?.level || 1;
                    insertAtIndex = i;
                    break;
                }
                if (data[i].isSectionHeader) {
                    referenceLevel = data[headerIdx + 1]?.level || 1; // Use level of item just after header if exists
                    insertAtIndex = i;
                    break;
                }
            }
            // If no break, means we are at the end of this section or list
            if (insertAtIndex < 0) {
                if (headerIdx < data.length - 1 && data[headerIdx + 1] && data[headerIdx + 1].type !== 'SECTION_HEADER') {
                    referenceLevel = data[headerIdx + 1].level; // use level of first item in section
                }
                insertAtIndex = data.findIndex((r, idx) => idx > headerIdx && r.type === 'DATA_CALCULATED_TOTAL');
                if (insertAtIndex < 0) { // still not found, look for next section header or end of data
                    insertAtIndex = data.findIndex((r, idx) => idx > headerIdx && r.isSectionHeader);
                    if (insertAtIndex < 0) insertAtIndex = data.length;
                }
            }
        } else { // Section header not found (should not happen with current structure if sectionId is valid)
            insertAtIndex = data.length;
        }

        console.log('category' + category);
        const newLabel = category || 'New Expense Item';
        const newId = `expense-${newLabel.replace(/[^a-zA-Z0-9]/g, '') || Math.random().toString(36).substr(2, 5)}`;
        // const newId = `new-cli-${Date.now()}`; // This is a client-side ID for the new parent row

        const newRow = {
            id: newId, label: newLabel, isSectionHeader: false, type: 'DATA_EXPENSE', isEditable: true,
            level: referenceLevel, rowClass: 'data-row', indentStyle: `padding-left: ${referenceLevel * 1.5}rem;`,
            showDeleteButton: true, // Allow deleting newly added UI rows
            salesforceIds: [], // No SF ID yet for parent
            weeks: this.weekColumns.map(col => ({
                id: `cell-${newId}-${col.id}`, weekIdentifier: col.id, value: 0, date: col.date,
                rowLabel: newLabel, category: newLabel, expenseCategory: newLabel, hasPopover: true,
                cellClass: 'slds-text-align_center cell-clickable cell-difference wholelightblue',
                variableFixed: 'Fixed', paymentFrequency: 'Weekly', weeklyAmount: 0, paymentTerm: 'Net 30',
                isDifference: true,
                salesforceIds: [], // No parent SF ID for the cell yet
                originalLineItems: [] // No children yet
            })),
            calculatedTotal: 0
        };

        data.splice(insertAtIndex, 0, newRow);
        this.cashFlowData = data;
        this.recalculateAllTotals();
        this.showToast('Success', `Added row “${newLabel}” locally. Save to persist.`, 'success');
    }

    handleDeleteRow(event) {
        const { rowId } = event.detail;
        // If this rowId corresponds to a saved record, mark parent CLI for deletion in global save.
        // For now, just removes from UI.
        this.cashFlowData = this.cashFlowData.filter(row => row.id !== rowId);
        this.recalculateAllTotals();
        this.showToast('Success', 'Row removed locally.', 'success');
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant, mode: 'dismissable' }));
    }

    uploadExcel() {
        console.log('upload excel called');
    }

    exportExcel() {
        console.log('export Excel called');
    }

    get isUploadDisabled() {
        return !this.file;
    }

    // open the modal
    openModal() {
        this.uploadModalOpen = true;
    }

    // close & reset
    closeModal() {
        this.uploadModalOpen = false;
        this.file = undefined;
        // also clear the input if you re-open
        const fileInput = this.template.querySelector('lightning-input[type="file"]');
        if (fileInput) fileInput.value = null;
    }

    // capture the File object
    handleFileChange(event) {
        this.file = event.target.files[0];
    }

    // read it into a Blob and do whatever you need
    // processUpload() {
    //     if (!this.file) { return; }
    //     const reader = new FileReader();
    //     reader.onload = () => {
    //         // result is an ArrayBuffer
    //         const arrayBuffer = reader.result;
    //         const blob = new Blob([arrayBuffer], { type: this.file.type });

    //         // — now you have your Excel as a Blob! —
    //         // e.g. call an @AuraEnabled Apex, or parse with SheetJS, etc.
    //         console.log('Excel blob:', blob);

    //         // then close
    //         this.closeModal();
    //     };
    //     reader.readAsArrayBuffer(this.file);
    // }

    //     processUpload() {
    //         console.log('file enter');
    //   if (!this.file) return;
    //     console.log('file pass');
    //   const reader = new FileReader();
    //   reader.onload = () => {
    //     console.log('enter onload');
    //     // reader.result is something like "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,ABCDEF..."
    //     const dataUrl = reader.result;
    //     const base64  = dataUrl.split(',')[1]; // grab everything after the comma
    //     // const base64 = reader.result.split(',')[1];
    //     // now you have exactly the string you can stick into your JSON payload
    //     const payload = {
    //       fileContent: base64
    //     };
    //     console.log('base 64 '+base64);
    //      console.log(JSON.stringify(payload));
    //     // example: call your REST endpoint
    //     // fetch('https://1edara8m8g.execute-api.us-east-1.amazonaws.com/parse-xls', {
    //     //   method: 'POST',
    //     //   headers: { 'Content-Type': 'application/json' },
    //     //   body: JSON.stringify(payload)
    //     // })
    //     // .then(r => r.json())
    //     // .then(console.log)
    //     // .catch(console.error);
    // //     uploadAndParseExcel({ base64Excel: base64 })
    // //       .then(responseBody => {
    // //         console.log('apec called and answered');
    // //         console.log('Apex returned:', responseBody);
    // //       })
    // //       .catch(error => {
    // //         console.log('error');
    // //         console.error('Apex error:', error);
    // //       });
    // //        this.closeModal();
    // //   };


    // }

    processUpload() {
        if (!this.file) return;

        const reader = new FileReader();
        reader.onload = () => {
            // reader.result is something like "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,ABCDEF..."
            const dataUrl = reader.result;
            const base64 = dataUrl.split(',')[1]; // grab everything after the comma
            console.log('base64' + base64);
            // now you have exactly the string you can stick into your JSON payload
            // const payload = {
            //     fileContent: base64
            // };

            // example: call your REST endpoint
            uploadAndParseExcel({ base64Excel: base64 })
                .then(responseBody => {
                    console.log('apec called and answered');
                    console.log('Apex returned:', responseBody);
                })
                .catch(error => {
                    console.log('error');
                    console.error('Apex error:', error);
                });
            this.closeModal();
        };

        reader.readAsDataURL(this.file);
    }

    handleMenuSelect(event) {
        const action = event.detail.value;
        if (action === 'save') {
            this.saveAs = false;
            this.handleGlobalSave();
        } else if (action === 'saveAs') {
            this.saveAs = true;
            console.log('called save as function');
            this.handleGlobalSave();
        }
    }

    transformGrid() {
        console.log('Labels- ' + this.cashFlowData.map(r => r.label));
        // 1) Grab the list of dates from your weekColumns
        const allDates = (this.weekColumns || []).map(col => col.date);

        // 2) Seed your output object
        const output = {};
        allDates.forEach(date => {
            output[date] = {
                payApp: 0,
                retainagePerc: 0,
                payroll: 0,
                subContractors: 0,
                materials: 0,
                equipment: 0,
                bondPremium: 0,
                misc: 0
            };
        });

        // 3) Make sure these exactly match your row.label values!
        const labelToKey = {
            'Pay App to be Submitted': 'payApp',
            'Less Retainage': 'retainagePerc',
            'Direct Payroll': 'payroll',
            'MiServ Subcontractors': 'subContractors',
            'Materials': 'materials',
            'Equipment': 'equipment',
            'Bond Premium': 'bondPremium',
            'OTHER SSV PAYMENTS REQ\'d': 'misc'
        };

        // 4) Walk your grid rows
        const gridData = this.cashFlowData || [];
        gridData.forEach(row => {
            const key = labelToKey[row.label];
            if (!key) return;   // skip anything not in our map

            row.weeks.forEach(cell => {
                if (cell.date && output[cell.date]) {
                    // use parseFloat in case values are strings
                    output[cell.date][key] = parseFloat(cell.value) || 0;
                }
            });
        });

        console.log('json grid converted -> ' + JSON.stringify(output));
    }


}
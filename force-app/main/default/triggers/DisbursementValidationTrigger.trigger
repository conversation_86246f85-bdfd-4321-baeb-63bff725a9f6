trigger DisbursementValidationTrigger on Disbursement__c (before insert, before update) {
    // Collect Disbursement Request IDs from Trigger.new
    Set<Id> requestIds = new Set<Id>();
    for (Disbursement__c disbursement : Trigger.new) {
        if(disbursement.Disbursement_Request__c != null) {
            requestIds.add(disbursement.Disbursement_Request__c);
        }
    }

    // Call the Apex class method to handle the validation and logging
    // Check if the trigger is active
    if (TriggerSettingHelper.isTriggerActive('DisbursementValidationTrigger')) {
        DisbursementValidationHandler.preventDuplicateDisbursements(requestIds, Trigger.new);
    }
}